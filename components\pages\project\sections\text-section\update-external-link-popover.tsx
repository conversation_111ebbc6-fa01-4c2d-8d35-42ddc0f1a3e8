import React, { useState } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  deleteExternalLinkAction,
  updateExternalLinkAction,
} from "@/actions/external-link";
import { ExternalLink } from "@prisma/client";
import Form from "next/form";
import { toast } from "sonner";

type Props = {
  children: React.ReactNode;
  externalLink: ExternalLink | null;
};

const UpdateExternalLinkPopover = ({ children, externalLink }: Props) => {
  const [label, setLabel] = useState(externalLink?.label || "");
  const [url, setUrl] = useState(externalLink?.url || "");
  const [isPending, setIsPending] = useState(false);
  if (!externalLink) return null;

  const handleUpdateExternalLink = async () => {
    if (!externalLink) return;
    setIsPending(true);
    const { data, message, error } = await updateExternalLinkAction({
      id: externalLink.id,
      label,
      url,
    });

    if (data) {
      toast.success(message);
      setIsPending(false);
    }

    if (error) {
      toast.error(message);
      setIsPending(false);
    }
  };

  return (
    <>
      <Popover>
        <PopoverTrigger asChild>{children}</PopoverTrigger>
        <PopoverContent align="start" className="w-96">
          <div className="grid gap-4">
            <div>
              <h3 className="font-semibold text-foreground">Update Link</h3>
              <hr className="border-border" />
              <p className="text-muted-foreground text-sm">
                Update the label and URL for the external link.
              </p>
            </div>

            <Form className="grid gap-2" action={handleUpdateExternalLink}>
              <div className="space-y-2">
                <Label htmlFor="label">Label</Label>
                <Input
                  placeholder="Check Out!"
                  type="text"
                  id="label"
                  defaultValue={externalLink?.label || ""}
                  onChange={(e) => setLabel(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="url">URL</Label>
                <Input
                  placeholder="http://example.com"
                  type="text"
                  id="url"
                  defaultValue={externalLink?.url}
                  onChange={(e) => setUrl(e.target.value)}
                />
              </div>
              <Button>{isPending ? "Updating..." : "Update"}</Button>
            </Form>
            <Button
              variant="destructive"
              onClick={async () => {
                await deleteExternalLinkAction(externalLink.id);
              }}
            >
              Delete Link
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
};

export default UpdateExternalLinkPopover;
