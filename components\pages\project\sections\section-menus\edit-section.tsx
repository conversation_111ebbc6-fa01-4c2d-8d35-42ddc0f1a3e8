import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Edit, ImagesIcon } from "lucide-react";
import { Section } from "@prisma/client";

type Props = {
  section: Section;
};

const EditSection = ({ section }: Props) => {
  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="Change Image"
      >
        <ImagesIcon className="h-4 w-4" />
      </Button>
    </>
  );
};

export default EditSection;
