"use client";

import React, { useState } from "react";
import {
  Section,
  Gallery,
  Image as ImageType,
  SectionItem,
} from "@prisma/client";
import Image from "next/image";
import {
  ChevronLeft,
  ChevronRight,
  Grid3X3,
  LayoutGrid,
  Shuffle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

type Props = {
  section: Section & {
    items: (SectionItem & {
      gallery:
        | (Gallery & {
            images: ImageType[];
          })
        | null;
    })[];
  };
};

const GallarySection = ({ section }: Props) => {
  const [viewMode, setViewMode] = useState<"grid" | "slider" | "carousel">(
    "grid"
  );
  const [currentIndex, setCurrentIndex] = useState(0);
  const { gallery } = section.items?.[0] || {};

  if (!gallery?.images || gallery.images.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No images in gallery</p>
      </div>
    );
  }

  const images = gallery.images;

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* View Toggle Controls */}
      <div className="flex justify-end mb-6">
        <div className="flex items-center gap-2 bg-background border rounded-lg p-1">
          <Button
            variant={viewMode === "grid" ? "primary" : "ghost"}
            size="sm"
            onClick={() => setViewMode("grid")}
            className="flex items-center gap-2"
          >
            <Grid3X3 className="w-4 h-4" />
            Grid
          </Button>
          <Button
            variant={viewMode === "slider" ? "primary" : "ghost"}
            size="sm"
            onClick={() => setViewMode("slider")}
            className="flex items-center gap-2"
          >
            <Shuffle className="w-4 h-4" />
            Slider
          </Button>
          <Button
            variant={viewMode === "carousel" ? "primary" : "ghost"}
            size="sm"
            onClick={() => setViewMode("carousel")}
            className="flex items-center gap-2"
          >
            <LayoutGrid className="w-4 h-4" />
            Carousel
          </Button>
        </div>
      </div>

      {/* Grid View */}
      {viewMode === "grid" && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {images.map((image) => (
            <div
              key={image.id}
              className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-900 p-1 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
            >
              {/* Gradient border effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-xl opacity-0 group-hover:opacity-75 transition-opacity duration-500 blur-sm"></div>
              <div className="relative bg-background rounded-lg overflow-hidden aspect-square">
                <Image
                  src={image.src}
                  alt={image.alt}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Carousel View */}
      {viewMode === "slider" && (
        <div className="relative">
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-900 p-1 hover:shadow-2xl transition-all duration-500">
            {/* Gradient border effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl opacity-0 hover:opacity-75 transition-opacity duration-500 blur-sm"></div>
            <div className="relative bg-background rounded-lg overflow-hidden aspect-video">
              <Image
                src={images[currentIndex].src}
                alt={images[currentIndex].alt}
                fill
                className="object-cover transition-transform duration-500"
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 80vw, 60vw"
              />
            </div>
          </div>

          {/* Navigation buttons */}
          {images.length > 1 && (
            <>
              <Button
                variant="secondary"
                size="sm"
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/10 backdrop-blur-md border-white/20 text-white hover:bg-white/20 shadow-lg"
                onClick={prevImage}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/10 backdrop-blur-md border-white/20 text-white hover:bg-white/20 shadow-lg"
                onClick={nextImage}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </>
          )}

          {/* Image counter */}
          {images.length > 1 && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm">
              {currentIndex + 1} / {images.length}
            </div>
          )}

          {/* Thumbnail indicators */}
          <div className="flex justify-center mt-4 gap-2">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-primary scale-125"
                    : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                }`}
              />
            ))}
          </div>
        </div>
      )}

      {/* Shadcn Carousel View */}
      {viewMode === "carousel" && (
        <div className="w-full max-w-7xl mx-auto">
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {images.map((image) => (
                <CarouselItem
                  key={image.id}
                  className="pl-2 md:pl-4 basis-1/2 sm:basis-1/3 md:basis-1/4 lg:basis-1/5"
                >
                  <div className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div className="p-0">
                      <div className="relative group overflow-hidden rounded-xl bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-900 p-1">
                        {/* Gradient border effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-xl opacity-0 group-hover:opacity-75 transition-opacity duration-500 blur-sm"></div>
                        <div className="relative bg-background rounded-lg overflow-hidden">
                          <div className="relative h-48 w-full">
                            <Image
                              src={image.src}
                              alt={image.alt}
                              fill
                              className="object-cover transition-transform duration-300 group-hover:scale-105"
                              sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                            />
                          </div>
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-end">
                            <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <h3 className="font-semibold text-sm">
                                {image.src}
                              </h3>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="left-2" />
            <CarouselNext className="right-2" />
          </Carousel>
        </div>
      )}
    </div>
  );
};

export default GallarySection;
