"use server";

import db from "@/lib/db";

export const createSection = async (data: any) => {
  try {
    const section = await db.$transaction(async (tx) => {
      await tx.section.updateMany({
        where: {
          projectId: data.projectId,
          index: {
            gte: data.index,
          },
        },
        data: {
          index: {
            increment: 1,
          },
        },
      });

      const newSection = await tx.section.create({
        data: {
          projectId: data.projectId,
          index: +data.index,
          type: data.type,
        },
        include: {
          items: true,
        },
      });

      const newSectionItem = await tx.sectionItem.create({
        data: {
          sectionId: newSection.id,
        },
      });

      let datas;

      if (data.type === "TEXT" && newSection.items) {
        datas = await tx.text.create({
          data: {
            sectionItemId: newSectionItem.id as string,
            content:
              "<h1> This is you heading </h1> <p> you can write as much as you want here. you can write a long paragraph or you can write a short paragraph. click here to check out. </p>",
          },
        });
      }

      if (data.type === "IMAGE" && newSection.items) {
        datas = await tx.image.create({
          data: {
            sectionItemId: newSectionItem.id as string,
          },
        });
      }

      if (data.type === "TEXTIMAGE" && newSection.items) {
        datas = await tx.text.create({
          data: {
            sectionItemId: newSectionItem.id,
          },
        });

        datas = await tx.image.create({
          data: {
            sectionItemId: newSectionItem.id,
          },
        });
      }

      if (data.type === "VIDEO" && newSection.items) {
        datas = await tx.video.create({
          data: {
            sectionItemId: newSectionItem.id,
            src: data.src,
          },
        });
      }

      if (data.type === "GALLERY" && newSection.items) {
        datas = await tx.gallery.create({
          data: {
            sectionItemId: newSectionItem.id,
          },
        });

        await tx.image.create({
          data: {
            galleryId: datas.id,
          },
        });

        await tx.image.create({
          data: {
            galleryId: datas.id,
          },
        });
        await tx.image.create({
          data: {
            galleryId: datas.id,
          },
        });
        await tx.image.create({
          data: {
            galleryId: datas.id,
          },
        });
        await tx.image.create({
          data: {
            galleryId: datas.id,
          },
        });
      }
    });

    return section;
  } catch (error) {
    console.error("Error creating section:", error);
    return;
  }
};

//delete section
export const deleteSection = async (section: any) => {
  try {
    const sections = await db.$transaction(async (tx) => {
      await tx.section.updateMany({
        where: {
          projectId: section.projectId,
          index: {
            gte: section.index,
          },
        },
        data: {
          index: {
            decrement: 1,
          },
        },
      });

      return await tx.section.delete({
        where: {
          id: section.id,
        },
      });
    });

    return sections;
  } catch (error) {
    console.error("Error deleting section:", error);
    return;
  }
};

export const isSection = async (id: string) => {
  const section = await db.section.findUnique({
    where: {
      id,
    },
    include: {
      items: true,
    },
  });

  return section;
};

export const isSectionItem = async (id: string) => {
  const section = await db.sectionItem.findUnique({
    where: {
      id,
    },
    include: {
      section: true,
    },
  });

  return section;
};

export const updateSectionTitle = async (data: any) => {
  try {
    const section = await db.section.update({
      where: {
        id: data.id,
      },
      data: {
        title: data.title,
      },
    });

    return section;
  } catch (error) {
    console.error("Error updating section title:", error);
    return;
  }
};

export const updateSectionIsReverse = async (data: any) => {
  try {
    const section = await db.sectionItem.update({
      where: {
        id: data.id,
      },
      data: {
        is_reverse: data.is_reverse,
      },
    });

    return section;
  } catch (error) {
    console.error("Error updating section is_reverse:", error);
    return;
  }
};

export const updateSectionOrder = async (data: {
  projectId: string;
  sectionOrders: { id: string; index: number }[];
}) => {
  try {
    const result = await db.$transaction(async (tx) => {
      // Update all sections with their new indices
      const updatePromises = data.sectionOrders.map((sectionOrder) =>
        tx.section.update({
          where: {
            id: sectionOrder.id,
          },
          data: {
            index: sectionOrder.index,
          },
        })
      );

      return await Promise.all(updatePromises);
    });

    return result;
  } catch (error) {
    console.error("Error updating section order:", error);
    return;
  }
};
