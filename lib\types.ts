import { getProject } from "@/actions/projects";
import {
  Ecommerce,
  Prisma,
  Section,
  SectionItem,
  Text,
  Image,
  ExternalLink,
  TextImage,
  Video,
  Gallery,
  Social,
  Map,
  Form,
} from "@prisma/client";

export enum StatusCode {
  Ok = 200,
  Created = 201,
  BadRequest = 400,
  Unauthorized = 401,
  Forbidden = 403,
  NotFound = 404,
  InternalServerError = 500,
}

export type ActionState<T> = {
  code: StatusCode;
  message: string;
  data?: T;
  error?: Error;
  success?: boolean;
};

export type ProjectWithAll = NonNullable<
  Prisma.PromiseReturnType<typeof getProject>
>;

export type ProjectWithAllSections = {
  sections: Section & {
    items: (SectionItem & {
      text: Text | null;
      image: Image | null;
      externalLink: ExternalLink | null;
      textImage: TextImage | null;
      video: Video | null;
      gallery: Gallery | null;
      social: Social | null;
      map: Map | null;
      form: Form | null;
      ecommerce: Ecommerce | null;
    })[];
  };
};

// Generic CSS property configuration
export interface CssPropertyConfig {
  key: string;
  label: string;
  type: "input" | "select" | "textarea";
  placeholder?: string;
  options?: { value: string; label: string }[];
  category: string;
}

// Generic props interface
export interface GenericCssPropertiesFormProps {
  children?: React.ReactNode;
  title?: string;
  description?: string;
  entityId: string;
  entityType: string;
  initialStyles?: Record<string, any>;
  onSave: (styles: Record<string, any>) => Promise<void>;
  onLoad?: () => Promise<Record<string, any> | null>;
  propertyConfigs?: CssPropertyConfig[];
  className?: string;
}
