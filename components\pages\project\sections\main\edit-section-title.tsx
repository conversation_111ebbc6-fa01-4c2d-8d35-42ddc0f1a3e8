"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Edit2 } from "lucide-react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Section } from "@prisma/client";
import Form from "next/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { updateSectionTitleAction } from "@/actions/section";

type Props = {
  section: Section;
};

const EditSectionTitle = ({ section }: Props) => {
  const [title, setTitle] = useState(section.title || "");

  const handleUpdateSectionTitle = async () => {
    const { data, message, error } = await updateSectionTitleAction({
      id: section.id,
      title,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };

  return (
    <>
      <Dialog>
        <DialogTrigger asChild>
          <Button
            variant="plain"
            size="icon"
            className="w-3 h-3 ml-2 cursor-pointer"
          >
            <Edit2 className="h-3 w-3" />
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Section Title</DialogTitle>
            <DialogDescription>{section.title}</DialogDescription>
            <Form className="space-y-2" action={handleUpdateSectionTitle}>
              <Input
                type="text"
                id="section-title"
                placeholder="Section Title"
                className="peer text-xs"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />

              <Button type="submit" className="mr-2">
                Update
              </Button>
              <DialogClose asChild>
                <Button variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
            </Form>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EditSectionTitle;
