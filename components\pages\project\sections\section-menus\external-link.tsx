import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ExternalLink, Section, SectionItem, Text } from "@prisma/client";
import { Link } from "lucide-react";
import { toast } from "sonner";
import {
  createExternalSectionLinkAction,
  createExternalTextLinkAction,
  deleteExternalLinkAction,
} from "@/actions/external-link";

type Props = {
  section: Section & {
    items: (SectionItem & {
      text: Text | null;
      externalLink: ExternalLink | null;
    })[];
  };
};

const ExternalLinks = ({ section }: Props) => {
  const firstItem = section.items?.[0];
  const handleExternalLink = async () => {
    if (!firstItem?.text) {
      return toast.error("Please create a text section first");
    }

    // Check if external link already exists on this text
    const existingExternalLink = firstItem.externalLink;

    const { data, message, error } = existingExternalLink
      ? await deleteExternalLinkAction(existingExternalLink.id)
      : await createExternalTextLinkAction({
          textId: firstItem.text.id,
        });

    if (error) {
      toast.error(error.message);
    }

    if (data) {
      toast.success(message);
    }
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="External Link section"
        onClick={handleExternalLink}
      >
        <Link className="h-4 w-4" />
      </Button>
    </>
  );
};

export default ExternalLinks;
