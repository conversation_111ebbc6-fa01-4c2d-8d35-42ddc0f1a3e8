"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from "@tiptap/extension-text-align";
import {
  $Enums,
  ExternalLink,
  Section,
  SectionItem,
  Text,
} from "@prisma/client";
import { useCallback, useEffect, useState } from "react";
import { MenuBar } from "./text-editor-menubar";
import { Type } from "lucide-react";
import { TextStyleKit } from "@tiptap/extension-text-style";
import { toast } from "sonner";
import { updateTextAction } from "@/actions/text";
import { debounce, Direction, getNewPosition } from "@/lib/utils2";
import { Button } from "@/components/ui/button";
import UpdateExternalLinkPopover from "./update-external-link-popover";
import TextPosition from "./text-position";
import { cn } from "@/lib/utils";

type Props = {
  section: Section & {
    items: (SectionItem & {
      text: (Text & { externalLink: ExternalLink | null }) | null;
      externalLink: ExternalLink | null;
    })[];
  };
};

// Menu Bar Component

export default function TextEditor({ section: { items } }: Props) {
  // Get the first text item from the items array
  const firstItem = items?.[0];
  const { text } = firstItem || {};
  const { id, content, rowPosition: position, externalLink } = text || {};
  const [isEditing, setIsEditing] = useState(false);
  const [isMenuHovered, setIsMenuHovered] = useState(false);
  const [rowPosition, setRowPosition] = useState<$Enums.RowPosition>(
    position || $Enums.RowPosition.LEFT
  );
  let editor: any;

  const handleRowPositionChange = (
    position: $Enums.RowPosition,
    direction: Direction
  ) => {
    const newPosition = getNewPosition(position, direction);
    setRowPosition(newPosition);
  };

  useEffect(() => {
    if (editor && position !== rowPosition) {
      editor
        .chain()
        .focus("all")
        .setTextAlign(rowPosition.toLowerCase())
        .blur()
        .run();
    }
  }, [rowPosition, position, editor]);

  const debouncedAndUpdate = useCallback(
    debounce(
      async (
        id: Text["id"],
        content: Text["content"],
        rowPosition: $Enums.RowPosition
      ) => {
        const { data, message, error } = await updateTextAction({
          id,
          content,
          rowPosition,
        });

        if (data) {
          toast.success(message);
        }

        if (error) {
          toast.error(message);
        }
      },
      200
    ),
    []
  );

  editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      TextStyleKit.configure({
        backgroundColor: {
          types: ["textStyle"],
        },
        color: {
          types: ["textStyle"],
        },
        fontFamily: {
          types: ["textStyle"],
        },
        fontSize: {
          types: ["textStyle"],
        },
        lineHeight: {
          types: ["textStyle"],
        },
      }),
      // Note: Additional extensions like Link, Underline, Highlight, HorizontalRule
      // would need to be installed separately from @tiptap packages
    ],
    content: content || "",
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class:
          "prose focus:outline-none focus:border-none p-6 rounded-lg transition-all duration-200 max-w-full!",
      },
      handleKeyDown: (_view, event) => {
        // Custom keyboard shortcuts
        if (event.ctrlKey || event.metaKey) {
          switch (event.key) {
            case "b":
              event.preventDefault();
              editor?.chain().focus().toggleBold().run();
              return true;
            case "i":
              event.preventDefault();
              editor?.chain().focus().toggleItalic().run();
              return true;
            case "u":
              event.preventDefault();
              editor?.chain().focus().toggleUnderline?.().run();
              return true;
            case "`":
              event.preventDefault();
              editor?.chain().focus().toggleCode().run();
              return true;
            case "k":
              event.preventDefault();
              const url = window.prompt("Enter URL:");
              if (url) {
                editor?.chain().focus().setLink?.({ href: url }).run();
              }
              return true;
            case "e":
              if (event.shiftKey) {
                event.preventDefault();
                editor?.chain().focus().setTextAlign("center").run();
                return true;
              }
              break;
            case "l":
              if (event.shiftKey) {
                event.preventDefault();
                editor?.chain().focus().setTextAlign("left").run();
                return true;
              }
              break;
            case "r":
              if (event.shiftKey) {
                event.preventDefault();
                editor?.chain().focus().setTextAlign("right").run();
                return true;
              }
              break;
            case "\\":
              event.preventDefault();
              editor?.chain().focus().clearNodes().run();
              return true;
          }
        }
        return false;
      },
    },
    onFocus: () => setIsEditing(true),
    onBlur: () => {
      // Delay blur to allow menu interactions, but check if menu is hovered
      setTimeout(() => {
        if (!isMenuHovered) {
          setIsEditing(false);
        }
      }, 200);
    },
    onCreate: async ({ editor }) => {
      // Auto-save functionality can be added here
    },
    onUpdate: async ({ editor }) => {
      // Auto-save functionality can be added here
      if (!id) return;
      debouncedAndUpdate(id, editor.getHTML(), rowPosition);
    },
  });

  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-muted rounded w-5/6"></div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "group/editor relative flex",
        rowPosition === $Enums.RowPosition.CENTER
          ? "justify-center"
          : rowPosition === $Enums.RowPosition.RIGHT
          ? "justify-end"
          : "justify-start"
      )}
      style={{
        paddingTop: isEditing ? "5rem" : "0",
        transition: "padding-top 200ms ease",
      }}
    >
      {/* Floating menu bar - only visible when editing */}
      {isEditing && (
        <MenuBar editor={editor} setIsMenuHovered={setIsMenuHovered} />
      )}

      {/* Enhanced Editor content */}
      <div
        className={cn(
          "relative p-4 border-2 max-w-[900px] border-transparent hover:border-primary/20 transition-all duration-300 flex flex-col items-start",
          rowPosition === $Enums.RowPosition.CENTER && "items-center",
          rowPosition === $Enums.RowPosition.RIGHT && "items-end",
          isEditing
            ? "ring-2 ring-primary/30 bg-gradient-to-br from-background to-muted/10 border-primary/30 shadow-lg"
            : "hover:bg-gradient-to-br hover:from-muted/10 hover:to-muted/20",
          "rounded-xl group/editor"
        )}
      >
        {/* Modern edit indicator */}
        {!isEditing && (
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover/editor:opacity-100 transition-all duration-300 pointer-events-none z-10">
            <div className="bg-background/95 backdrop-blur-md border border-border/50 rounded-xl px-4 py-2 flex items-center gap-3 text-sm text-foreground shadow-xl">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse"></div>
              <Type className="h-4 w-4" />
              Click to edit text
            </div>
          </div>
        )}

        {/* Editor content with enhanced styling */}
        <div className="w-full relative">
          <EditorContent
            className={cn(
              "prose prose-lg max-w-none focus-within:outline-none",
              "prose-headings:text-foreground prose-p:text-foreground",
              "prose-strong:text-foreground prose-em:text-foreground",
              isEditing && "min-h-[100px]"
            )}
            editor={editor}
          />
        </div>

        {/* External link with modern styling */}
        {externalLink && (
          <div className="mt-4">
            <UpdateExternalLinkPopover externalLink={externalLink}>
              <Button
                variant="plain"
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300"
              >
                {externalLink?.label}
              </Button>
            </UpdateExternalLinkPopover>
          </div>
        )}

        {/* Enhanced text position controls */}
        <div className="absolute bottom-2 right-2 opacity-0 group-hover/editor:opacity-100 transition-opacity duration-300">
          <TextPosition
            rowPosition={rowPosition}
            onPositionChange={(direction) => {
              handleRowPositionChange(rowPosition, direction);
            }}
          />
        </div>
      </div>
    </div>
  );
}
