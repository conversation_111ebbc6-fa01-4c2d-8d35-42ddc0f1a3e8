"use client";

import React from "react";
import TextEditor from "../text-section/text-editor";
import ImageSection from "../image-section";
import {
  Section,
  Text,
  ExternalLink,
  Image as Images,
  $Enums,
  SectionItem,
} from "@prisma/client";
import { cn } from "@/lib/utils";
import { Reorder } from "motion/react";
import { updateSectionAction } from "@/actions/section";
import { toast } from "sonner";

type Props = {
  section: Section & {
    items: (SectionItem & {
      text: Text | null;
      externalLink: ExternalLink | null;
      image: Images | null;
    })[];
  };
};

const TextImageSection = ({ section }: Props) => {
  // Get the first item from the items array
  const firstItem = section.items?.[0];
  if (!firstItem?.text || !firstItem?.image) {
    return null;
  }

  const oders = [$Enums.SectionType.TEXT, $Enums.SectionType.IMAGE];
  const items = section && firstItem.is_reverse ? oders.reverse() : oders;

  const handleReorder = async () => {
    const { data, message, error } = await updateSectionAction({
      id: firstItem.id,
      is_reverse: !firstItem.is_reverse,
    });

    if (error) {
      toast.error(error.message);
    }

    if (data) {
      toast.success(message);
    }
  };

  return (
    <div className="w-full max-w-7xl mx-auto">
      <Reorder.Group
        className={cn("flex items-start gap-8 lg:gap-16")}
        axis="x"
        values={items}
        onReorder={handleReorder}
      >
        {items.map((item, index) => (
          <Reorder.Item
            key={item}
            value={item}
            className={cn(
              "flex-1 min-w-0",
              "transition-all duration-500 hover:scale-[1.02]",
              index === 0 ? "lg:flex-[1.2]" : "lg:flex-1"
            )}
          >
            {item === $Enums.SectionType.TEXT && (
              <div className="h-full flex items-center">
                <TextEditor section={section} />
              </div>
            )}

            {item === $Enums.SectionType.IMAGE && (
              <div className="h-full flex items-center">
                <ImageSection section={section} />
              </div>
            )}
          </Reorder.Item>
        ))}
      </Reorder.Group>
    </div>
  );
};

export default TextImageSection;
