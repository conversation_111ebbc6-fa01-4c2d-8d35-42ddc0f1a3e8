"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import Placeholder from "@/public/placeholder1.png";
import { Project } from "@prisma/client";
import ProjectTitleUpdateForm from "./project-title-update-form";
import ProjectDropdownMenu from "./project-dropdown-menu";
import Link from "next/link";
import { Calendar, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";

type Props = {
  project: Project;
};

const ProjectItem = ({
  project: { id, title, createdAt, updatedAt },
}: Props) => {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(new Date(date));
  };

  return (
    <Card className="group overflow-hidden transition-all duration-200 hover:shadow-lg hover:scale-105 border-border/50 hover:border-border">
      <div className="relative">
        {/* Project dropdown menu */}
        <ProjectDropdownMenu id={id} title={title} />

        {/* Project preview image */}
        <Link href={`/project/${id}`} className="block">
          <div className="relative overflow-hidden bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-900">
            <Image
              src={Placeholder}
              alt={`${title} preview`}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
            />
            {/* Overlay on hover */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
              <Button
                variant="secondary"
                size="sm"
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Open Project
              </Button>
            </div>
          </div>
        </Link>
      </div>

      <CardContent className="p-6 space-y-4">
        {/* Project title form */}
        <ProjectTitleUpdateForm title={title} id={id} />

        {/* Project metadata */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            <span>Created {formatDate(createdAt)}</span>
          </div>
          {updatedAt !== createdAt && (
            <div className="text-xs">Updated {formatDate(updatedAt)}</div>
          )}
        </div>

        {/* Quick actions */}
        <div className="flex items-center gap-2 pt-2">
          <Link href={`/project/${id}`} className="flex-1">
            <Button variant="outline" size="sm" className="w-full gap-2">
              <ExternalLink className="w-4 h-4" />
              Edit Project
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProjectItem;
