import { $Enums } from "@prisma/client";

//debounce
export const debounce = <T extends (...args: Parameters<T>) => void>(
  callback: T,
  delay: number = 200
) => {
  let timer: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      callback(...args);
    }, delay);
  };
};

export enum Direction {
  LEFT = "LEFT",
  RIGHT = "RIGHT",
  CENTER = "CENTER",
}

export const getNewPosition = (
  position: $Enums.RowPosition,
  direction: Direction
) => {
  if (position === $Enums.RowPosition.LEFT && direction === Direction.RIGHT) {
    return $Enums.RowPosition.CENTER;
  }
  if (position === $Enums.RowPosition.CENTER && direction === Direction.LEFT) {
    return $Enums.RowPosition.LEFT;
  }
  if (position === $Enums.RowPosition.CENTER && direction === Direction.RIGHT) {
    return $Enums.RowPosition.RIGHT;
  }
  if (position === $Enums.RowPosition.RIGHT && direction === Direction.LEFT) {
    return $Enums.RowPosition.CENTER;
  }
  return position;
};
