"use client";

import React, { useState } from "react";
import Section<PERSON>utton from "./section-button";
import {
  $Enums,
  ExternalLink,
  Project,
  Section,
  SectionItem,
  Text,
} from "@prisma/client";
import TextEditor from "../text-section/text-editor";
import SectionContainers from "./section-containers";
import { ProjectWithAll } from "@/lib/types";
import ImageSection from "../image-section";
import TextImageSection from "../text-image-section";
import VideoSection from "../video-section";
import GallarySection from "../gallary-section";
import SocialSection from "../social-section";
import MapSection from "../map-section";
import FormSection from "../form-section";
import EcommerceSection from "../ecommerce-section";
import { Reorder, AnimatePresence } from "framer-motion";
import { updateSectionOrderAction } from "@/actions/section";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { getSectionTypeIcon, getSectionTypeName } from "@/lib/static";

type Props = {
  sections: (Section & {
    items: (SectionItem & {
      text: (Text & { externalLink: ExternalLink | null }) | null;
      image: any | null;
      externalLink: any | null;
      textImage: any | null;
      video: any | null;
      gallery: any | null;
      social: any | null;
      map: any | null;
      form: any | null;
      ecommerce: any | null;
    })[];
  })[];
  projectId: Project["id"];
};

const SectionList = ({ sections, projectId }: Props) => {
  const [isDragging, setIsDragging] = useState(false);
  const [draggedSection, setDraggedSection] = useState<string | null>(null);

  const handleReorder = async (newSections: typeof sections) => {
    // Create section orders with new indices
    const sectionOrders = newSections.map((section, index) => ({
      id: section.id,
      index: index,
    }));

    const { data, message, error } = await updateSectionOrderAction({
      projectId,
      sectionOrders,
    });

    if (error) {
      toast.error(error.message);
    }

    if (data) {
      toast.success(message);
    }

    // Reset dragging state
    setIsDragging(false);
    setDraggedSection(null);
  };

  return (
    <div
      className={cn(
        "space-y-1 transition-all duration-300",
        isDragging &&
          "bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-3xl p-4"
      )}
    >
      {/* Drag instruction overlay */}
      {isDragging && (
        <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm font-medium rounded-full shadow-lg backdrop-blur-sm">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            Drag to reorder sections
          </div>
        </div>
      )}

      {/* Welcome message for empty state */}
      {sections.length === 0 && (
        <div className="text-center py-16 px-8">
          <div className="">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full" />
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-2">
              Start Building Your Website
            </h3>
            <p className="text-muted-foreground mb-6">
              Add your first section to begin creating your amazing website.
              Choose from text, images, videos, and more.
            </p>
          </div>
        </div>
      )}

      <Reorder.Group
        axis="y"
        values={sections}
        onReorder={handleReorder}
        className="space-y-0 relative"
        as="ul"
      >
        {sections.map((section, index) => (
          <Reorder.Item
            key={section.id}
            value={section}
            className={cn(
              "group relative",
              isDragging && draggedSection === section.id && "opacity-90"
            )}
            as="li"
            whileDrag={{
              scale: 1.02,
              zIndex: 50,
              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
              rotate: 1,
            }}
            whileHover={{ scale: 1.005 }}
            transition={{ duration: 0.2 }}
            onDragStart={() => {
              setIsDragging(true);
              setDraggedSection(section.id);
            }}
            onDragEnd={() => {
              setIsDragging(false);
              setDraggedSection(null);
            }}
          >
            {/* Position indicator */}
            <div
              className={cn(
                "absolute left-8 z-[99999] top-4 -translate-y-1/2 w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs font-bold flex items-center justify-center shadow-lg transition-all duration-300",
                isDragging
                  ? "opacity-100 scale-110"
                  : "opacity-0 group-hover:opacity-70"
              )}
            >
              {index + 1}
            </div>

            {/* Section type indicator */}
            <div
              className={cn(
                "absolute -right-0 z-[999999] top-4 px-2 py-1 rounded-lg bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 text-xs font-medium text-foreground backdrop-blur-sm transition-all duration-300",
                isDragging && draggedSection === section.id
                  ? "opacity-100 scale-110"
                  : "opacity-0 group-hover:opacity-100"
              )}
            >
              <div className="flex items-center gap-1">
                <span>{getSectionTypeIcon(section.type)}</span>
                <span>{getSectionTypeName(section.type)}</span>
              </div>
            </div>

            {/* Drop zone indicator */}
            {isDragging && draggedSection !== section.id && (
              <div className="absolute inset-0 border-2 border-dashed border-blue-500/30 rounded-2xl bg-blue-500/5 transition-all duration-200" />
            )}
            <SectionButton index={section.index} projectId={projectId} />
            <SectionContainers key={section.id} section={section}>
              {section.type === $Enums.SectionType.TEXT && (
                <TextEditor section={section} />
              )}
              {section.type === $Enums.SectionType.IMAGE && (
                <ImageSection section={section} />
              )}
              {section.type === $Enums.SectionType.TEXTIMAGE && (
                <TextImageSection section={section} />
              )}
              {section.type === $Enums.SectionType.VIDEO && (
                <VideoSection section={section} />
              )}
              {section.type === $Enums.SectionType.GALLERY && (
                <GallarySection section={section} />
              )}

              {section.type === $Enums.SectionType.SOCIAL && (
                <SocialSection section={section} />
              )}

              {section.type === $Enums.SectionType.MAP && (
                <MapSection section={section} />
              )}

              {section.type === $Enums.SectionType.FORM && (
                <FormSection section={section} />
              )}

              {section.type === $Enums.SectionType.ECOMMERCE && (
                <EcommerceSection section={section} />
              )}
              {section.type === $Enums.SectionType.BLANK && (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    This is a blank section
                  </p>
                </div>
              )}
            </SectionContainers>
          </Reorder.Item>
        ))}
      </Reorder.Group>

      <div className="group">
        <SectionButton index={sections.length} projectId={projectId} />
      </div>
    </div>
  );
};

export default SectionList;
