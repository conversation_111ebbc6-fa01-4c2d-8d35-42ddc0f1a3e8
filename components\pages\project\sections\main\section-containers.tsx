"use client";

import React, { useEffect, useRef } from "react";
import Container from "@/components/core/container";
import SectionMenu from "./section-menu";
import {
  ExternalLink,
  Section,
  SectionItem,
  Text,
  CssProperty,
} from "@prisma/client";
import EditSectionTitle from "./edit-section-title";

type Props = {
  children: React.ReactNode;
  section: Section & {
    items?: (SectionItem & {
      text?: Text | null;
      externalLink?: ExternalLink | null;
    })[];
    cssProperties?: CssProperty | null;
  };
};

const SectionContainers = ({ children, section }: Props) => {
  const sectionRef = useRef<HTMLElement>(null);

  // Apply custom CSS properties if they exist
  const customStyles =
    (section.cssProperties?.styles as Record<string, any>) || {};

  // Create base styles that can be overridden by custom styles
  const baseStyles = {
    overflow: "visible",
  };

  // Merge base styles with custom styles, giving priority to custom styles
  const finalStyles = {
    ...baseStyles,
    ...customStyles,
  };

  // Apply CSS properties with higher specificity using useEffect
  useEffect(() => {
    if (sectionRef.current && Object.keys(customStyles).length > 0) {
      const element = sectionRef.current;

      // Properties that should be applied to the content area instead of the container
      const contentProperties = [
        "color",
        "fontSize",
        "fontWeight",
        "textAlign",
        "lineHeight",
        "letterSpacing",
      ];

      // Properties that should not affect positioned elements (menus)
      const containerProperties = [
        "backgroundColor",
        "backgroundImage",
        "backgroundSize",
        "backgroundPosition",
        "backgroundRepeat",
        "backgroundAttachment",
        "padding",
        "margin",
        "borderRadius",
        "boxShadow",
        "border",
        "display",
        "justifyContent",
        "alignItems",
      ];

      // First, handle background properties in the correct order
      const backgroundProperties = [
        "backgroundImage",
        "backgroundColor",
        "backgroundSize",
        "backgroundPosition",
        "backgroundRepeat",
        "backgroundAttachment",
      ];
      const hasBackgroundImage =
        customStyles.backgroundImage &&
        customStyles.backgroundImage !== "" &&
        customStyles.backgroundImage !== "none";

      // Apply background properties first
      backgroundProperties.forEach((property) => {
        const value = customStyles[property];
        if (value !== undefined && value !== null && value !== "") {
          const cssProperty = property.replace(/([A-Z])/g, "-$1").toLowerCase();

          if (property === "backgroundColor") {
            if (!hasBackgroundImage) {
              // Only clear background-image if no custom background image is set
              element.style.setProperty(
                "background-image",
                "none",
                "important"
              );
            }
            element.style.setProperty(
              "background-color",
              String(value),
              "important"
            );
          } else if (property === "backgroundImage") {
            // Special handling for background image - ensure it's wrapped in url() if needed
            let imageValue = String(value);
            if (
              imageValue &&
              !imageValue.startsWith("url(") &&
              !imageValue.startsWith("linear-gradient") &&
              !imageValue.startsWith("radial-gradient")
            ) {
              imageValue = `url(${imageValue})`;
            }
            element.style.setProperty(cssProperty, imageValue, "important");
          } else {
            element.style.setProperty(cssProperty, String(value), "important");
          }
        }
      });

      // Then apply all other custom styles
      Object.entries(customStyles).forEach(([property, value]) => {
        if (
          value !== undefined &&
          value !== null &&
          value !== "" &&
          !backgroundProperties.includes(property)
        ) {
          // Convert camelCase to kebab-case for CSS properties
          const cssProperty = property.replace(/([A-Z])/g, "-$1").toLowerCase();

          if (contentProperties.includes(property)) {
            // Apply text/content properties to the content container
            const contentContainer = element.querySelector(".section-content");
            if (contentContainer) {
              (contentContainer as HTMLElement).style.setProperty(
                cssProperty,
                String(value),
                "important"
              );
            }
          } else if (containerProperties.includes(property)) {
            // Apply container properties to the main section
            element.style.setProperty(cssProperty, String(value), "important");
          } else {
            // Apply other properties to the main section
            element.style.setProperty(cssProperty, String(value), "important");
          }
        }
      });

      // Ensure menus remain visible by setting their z-index and position
      const sectionMenu = element.querySelector(".section-menu");
      if (sectionMenu) {
        (sectionMenu as HTMLElement).style.setProperty(
          "position",
          "absolute",
          "important"
        );
        (sectionMenu as HTMLElement).style.setProperty(
          "z-index",
          "50",
          "important"
        );
        (sectionMenu as HTMLElement).style.setProperty(
          "top",
          "1rem",
          "important"
        );
        (sectionMenu as HTMLElement).style.setProperty(
          "right",
          "1rem",
          "important"
        );
      }
    }
  }, [customStyles]);

  // Add debugging in development
  if (
    process.env.NODE_ENV === "development" &&
    Object.keys(customStyles).length > 0
  ) {
    console.log("Section CSS Properties:", {
      sectionId: section.id,
      customStyles,
      finalStyles,
    });
  }

  return (
    <section
      ref={sectionRef}
      className="relative group/section bg-gradient-to-br from-background via-background to-muted/20 border border-border/30 rounded-2xl my-6 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/5 hover:border-primary/20 hover:-translate-y-1"
      style={finalStyles}
    >
      {/* Modern gradient overlay - only show if no custom background */}
      {!customStyles.backgroundColor && (
        <>
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/3 via-purple-500/3 to-pink-500/3 opacity-0 group-hover/section:opacity-100 transition-all duration-500 rounded-2xl pointer-events-none" />
          {/* Subtle border glow effect */}
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 opacity-0 group-hover/section:opacity-100 transition-opacity duration-500 blur-sm -z-10" />
        </>
      )}

      {/* Section menu */}
      <div className="section-menu">
        <SectionMenu section={section} />
      </div>

      {/* Section content with enhanced padding */}
      <Container className="section-content py-8 px-6">{children}</Container>

      {/* Enhanced section index indicator */}
      <div className="absolute top-4 left-4 opacity-0 group-hover/section:opacity-100 transition-all duration-300 transform translate-y-2 group-hover/section:translate-y-0">
        <div className="bg-background/95 backdrop-blur-sm border border-border/50 text-foreground text-xs px-3 py-2 rounded-lg font-medium flex flex-row items-center gap-2 shadow-lg">
          <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
          {section.title || "Section " + (section.index + 1)}
          <EditSectionTitle section={section} />
        </div>
      </div>
    </section>
  );
};

export default SectionContainers;
