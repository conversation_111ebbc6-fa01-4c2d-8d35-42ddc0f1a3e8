# Generic CSS Properties Form System

## Overview

The Generic CSS Properties Form System is a flexible, reusable component system for managing CSS properties across different entity types in your application. It provides a consistent interface for editing CSS styles while allowing for customization based on specific use cases.

## Architecture

### Core Components

1. **GenericCssPropertiesForm** (`components/ui/css-properties-form.tsx`)
   - The main generic form component
   - Handles all common CSS properties
   - Provides both Quick Editor and JSON Editor modes
   - Fully customizable through property configurations

2. **Specialized Forms** (`components/ui/specialized-css-forms.tsx`)
   - Pre-configured forms for specific entity types
   - ImageCssPropertiesForm, ButtonCssPropertiesForm, TextCssPropertiesForm, VideoCssPropertiesForm
   - Each optimized for their respective use cases

3. **Entity-Specific Forms** (e.g., `components/pages/project/sections/section-menus/section-css-properties-form.tsx`)
   - Wrappers that connect the generic form to specific backend logic
   - Handle save/load operations for specific entities

## Key Features

### 1. **Flexible Property Configuration**
```typescript
interface CssPropertyConfig {
  key: string;           // CSS property name (camelCase)
  label: string;         // Display label
  type: "input" | "select" | "textarea";
  placeholder?: string;  // Input placeholder
  options?: { value: string; label: string }[]; // For select inputs
  category: string;      // Grouping category
}
```

### 2. **Dual Editor Modes**
- **Quick Editor**: Organized form fields grouped by category
- **JSON Editor**: Direct JSON editing with real-time sync

### 3. **Smart Value Handling**
- Automatic conversion between "default" and empty string values
- Proper CSS value formatting (e.g., wrapping URLs in `url()`)
- Real-time synchronization between Quick and JSON editors

### 4. **Extensible Categories**
- Layout & Display
- Spacing
- Colors & Background
- Typography
- Borders
- Effects
- Interactive

## Usage Examples

### Basic Usage (Default Properties)
```tsx
import { GenericCssPropertiesForm } from "@/components/ui/css-properties-form";

<GenericCssPropertiesForm
  entityId="my-element"
  entityType="element"
  onSave={handleSave}
  onLoad={handleLoad}
/>
```

### Custom Properties Configuration
```tsx
const customProperties: CssPropertyConfig[] = [
  {
    key: "width",
    label: "Width",
    type: "input",
    category: "Layout",
    placeholder: "100px, 50%, auto"
  },
  {
    key: "textAlign",
    label: "Text Align",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "left", label: "Left" },
      { value: "center", label: "Center" },
      { value: "right", label: "Right" }
    ]
  }
];

<GenericCssPropertiesForm
  propertyConfigs={customProperties}
  entityId="my-element"
  entityType="element"
  onSave={handleSave}
/>
```

### Using Specialized Forms
```tsx
import { ImageCssPropertiesForm } from "@/components/ui/specialized-css-forms";

<ImageCssPropertiesForm
  entityId="image-1"
  onSave={handleImageSave}
  onLoad={handleImageLoad}
/>
```

### Entity-Specific Implementation
```tsx
// components/pages/project/sections/section-menus/section-css-properties-form.tsx
import { GenericCssPropertiesForm } from "@/components/ui/css-properties-form";
import { updateCssPropertiesAction, getCssPropertiesAction } from "@/actions/css-properties";

const SectionCssPropertiesForm = ({ section }) => {
  const handleSave = async (styles: Record<string, any>) => {
    await updateCssPropertiesAction(section.id, styles);
  };

  const handleLoad = async () => {
    const cssProperties = await getCssPropertiesAction(section.id);
    return cssProperties?.styles || {};
  };

  return (
    <GenericCssPropertiesForm
      title="Section CSS Properties"
      entityId={section.id}
      entityType="section"
      onSave={handleSave}
      onLoad={handleLoad}
    />
  );
};
```

## Specialized Forms

### 1. ImageCssPropertiesForm
**Optimized for**: Images, media elements
**Key Properties**: width, height, objectFit, filter, borderRadius, boxShadow

### 2. ButtonCssPropertiesForm
**Optimized for**: Buttons, clickable elements
**Key Properties**: padding, backgroundColor, color, borderRadius, fontSize, fontWeight

### 3. TextCssPropertiesForm
**Optimized for**: Text elements, typography
**Key Properties**: fontFamily, fontSize, fontWeight, textAlign, lineHeight, color

### 4. VideoCssPropertiesForm
**Optimized for**: Video elements
**Key Properties**: Similar to images with media-specific options

## Integration with Backend

### Save Operation
```typescript
const handleSave = async (styles: Record<string, any>) => {
  try {
    // Your backend save logic
    await updateEntityStyles(entityId, styles);
    toast.success("Styles saved successfully!");
  } catch (error) {
    console.error("Error saving styles:", error);
    throw error; // Re-throw to let form handle error
  }
};
```

### Load Operation
```typescript
const handleLoad = async () => {
  try {
    const entity = await getEntityStyles(entityId);
    return entity?.styles || {};
  } catch (error) {
    console.error("Error loading styles:", error);
    return null;
  }
};
```

## CSS Application

The system works with the CSS application logic in `section-containers.tsx` which:

1. **Processes background properties first** to handle conflicts properly
2. **Wraps URLs in `url()`** for background images
3. **Applies properties with `!important`** to override Tailwind classes
4. **Categorizes properties** for targeted application (container vs content)

## Benefits

### 1. **Reusability**
- One form system for all entity types
- Consistent UI/UX across the application
- Reduced code duplication

### 2. **Flexibility**
- Easily customizable property sets
- Support for different input types
- Extensible category system

### 3. **Maintainability**
- Centralized form logic
- Type-safe property configurations
- Clear separation of concerns

### 4. **User Experience**
- Dual editor modes (Quick + JSON)
- Real-time synchronization
- Organized property categories
- Smart value handling

## Future Extensions

### 1. **Additional Property Types**
- Color picker inputs
- Range sliders for numeric values
- File upload for background images

### 2. **Advanced Features**
- CSS property validation
- Live preview of changes
- Undo/redo functionality
- Property presets/themes

### 3. **Entity-Specific Enhancements**
- Context-aware property suggestions
- Entity-specific validation rules
- Responsive design properties

## Best Practices

1. **Use specialized forms** when available for better UX
2. **Create custom property configs** for specific use cases
3. **Handle errors gracefully** in save/load operations
4. **Provide meaningful placeholders** and labels
5. **Group related properties** in logical categories
6. **Test with real CSS values** to ensure proper application
