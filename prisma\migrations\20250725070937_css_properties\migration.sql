-- CreateTable
CREATE TABLE "css_properties" (
    "id" TEXT NOT NULL,
    "styles" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "sectionId" TEXT,

    CONSTRAINT "css_properties_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "properties" (
    "id" TEXT NOT NULL,
    "layoutId" TEXT,
    "spacingId" TEXT,
    "borderId" TEXT,
    "backgroundId" TEXT,
    "typographyId" TEXT,
    "flexboxId" TEXT,
    "gridId" TEXT,
    "visualId" TEXT,
    "interactionId" TEXT,
    "objectId" TEXT,
    "animationId" TEXT,
    "extraId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "properties_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LayoutCss" (
    "id" TEXT NOT NULL,
    "display" TEXT,
    "position" TEXT,
    "top" TEXT,
    "right" TEXT,
    "bottom" TEXT,
    "left" TEXT,
    "zIndex" TEXT,
    "visibility" TEXT,
    "boxSizing" TEXT,
    "isolation" TEXT,

    CONSTRAINT "LayoutCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SpacingCss" (
    "id" TEXT NOT NULL,
    "margin" TEXT,
    "padding" TEXT,
    "gap" TEXT,
    "rowGap" TEXT,
    "columnGap" TEXT,

    CONSTRAINT "SpacingCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BorderCss" (
    "id" TEXT NOT NULL,
    "border" TEXT,
    "borderRadius" TEXT,
    "borderColor" TEXT,
    "borderWidth" TEXT,
    "borderStyle" TEXT,

    CONSTRAINT "BorderCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BackgroundCss" (
    "id" TEXT NOT NULL,
    "backgroundColor" TEXT,
    "backgroundImage" TEXT,
    "backgroundSize" TEXT,
    "backgroundRepeat" TEXT,
    "backgroundPosition" TEXT,
    "backgroundAttachment" TEXT,

    CONSTRAINT "BackgroundCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TypographyCss" (
    "id" TEXT NOT NULL,
    "fontFamily" TEXT,
    "fontSize" TEXT,
    "fontWeight" TEXT,
    "lineHeight" TEXT,
    "letterSpacing" TEXT,
    "textAlign" TEXT,
    "textTransform" TEXT,
    "textDecoration" TEXT,
    "textShadow" TEXT,
    "whiteSpace" TEXT,
    "wordBreak" TEXT,
    "color" TEXT,

    CONSTRAINT "TypographyCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FlexboxCss" (
    "id" TEXT NOT NULL,
    "flexDirection" TEXT,
    "flexWrap" TEXT,
    "justifyContent" TEXT,
    "alignItems" TEXT,
    "alignContent" TEXT,
    "flex" TEXT,
    "flexGrow" TEXT,
    "flexShrink" TEXT,
    "flexBasis" TEXT,
    "order" TEXT,
    "flexFlow" TEXT,

    CONSTRAINT "FlexboxCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "GridCss" (
    "id" TEXT NOT NULL,
    "gridTemplateColumns" TEXT,
    "gridTemplateRows" TEXT,
    "gridTemplateAreas" TEXT,
    "gridAutoFlow" TEXT,
    "gridAutoColumns" TEXT,
    "gridAutoRows" TEXT,
    "gridColumnGap" TEXT,
    "gridRowGap" TEXT,
    "gridGap" TEXT,
    "gridColumn" TEXT,
    "gridRow" TEXT,
    "gridArea" TEXT,
    "grid" TEXT,
    "justifySelf" TEXT,
    "alignSelf" TEXT,
    "placeSelf" TEXT,
    "justifyItems" TEXT,
    "placeContent" TEXT,

    CONSTRAINT "GridCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VisualCss" (
    "id" TEXT NOT NULL,
    "boxShadow" TEXT,
    "opacity" TEXT,
    "filter" TEXT,
    "transform" TEXT,
    "transition" TEXT,
    "mixBlendMode" TEXT,
    "clipPath" TEXT,
    "willChange" TEXT,

    CONSTRAINT "VisualCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InteractionCss" (
    "id" TEXT NOT NULL,
    "cursor" TEXT,
    "userSelect" TEXT,
    "pointerEvents" TEXT,

    CONSTRAINT "InteractionCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ObjectCss" (
    "id" TEXT NOT NULL,
    "objectFit" TEXT,
    "objectPosition" TEXT,

    CONSTRAINT "ObjectCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AnimationCss" (
    "id" TEXT NOT NULL,
    "animation" TEXT,
    "animationName" TEXT,
    "animationDuration" TEXT,
    "animationTimingFunction" TEXT,
    "animationDelay" TEXT,
    "animationIterationCount" TEXT,
    "animationDirection" TEXT,
    "animationFillMode" TEXT,
    "animationPlayState" TEXT,

    CONSTRAINT "AnimationCss_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ExtraCss" (
    "id" TEXT NOT NULL,
    "aspectRatio" TEXT,
    "direction" TEXT,
    "writingMode" TEXT,
    "outline" TEXT,
    "outlineColor" TEXT,
    "outlineWidth" TEXT,
    "outlineOffset" TEXT,
    "scrollBehavior" TEXT,
    "borderCollapse" TEXT,
    "borderSpacing" TEXT,
    "tableLayout" TEXT,
    "captionSide" TEXT,
    "listStyle" TEXT,
    "listStyleType" TEXT,
    "listStylePosition" TEXT,
    "contentVisibility" TEXT,
    "contain" TEXT,
    "columnCount" TEXT,
    "columnGap" TEXT,
    "columnWidth" TEXT,
    "columnRule" TEXT,

    CONSTRAINT "ExtraCss_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "css_properties_sectionId_key" ON "css_properties"("sectionId");

-- CreateIndex
CREATE INDEX "css_properties_sectionId_idx" ON "css_properties"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_layoutId_key" ON "properties"("layoutId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_spacingId_key" ON "properties"("spacingId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_borderId_key" ON "properties"("borderId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_backgroundId_key" ON "properties"("backgroundId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_typographyId_key" ON "properties"("typographyId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_flexboxId_key" ON "properties"("flexboxId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_gridId_key" ON "properties"("gridId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_visualId_key" ON "properties"("visualId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_interactionId_key" ON "properties"("interactionId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_objectId_key" ON "properties"("objectId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_animationId_key" ON "properties"("animationId");

-- CreateIndex
CREATE UNIQUE INDEX "properties_extraId_key" ON "properties"("extraId");

-- AddForeignKey
ALTER TABLE "css_properties" ADD CONSTRAINT "css_properties_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "sections"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_layoutId_fkey" FOREIGN KEY ("layoutId") REFERENCES "LayoutCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_spacingId_fkey" FOREIGN KEY ("spacingId") REFERENCES "SpacingCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_borderId_fkey" FOREIGN KEY ("borderId") REFERENCES "BorderCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_backgroundId_fkey" FOREIGN KEY ("backgroundId") REFERENCES "BackgroundCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_typographyId_fkey" FOREIGN KEY ("typographyId") REFERENCES "TypographyCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_flexboxId_fkey" FOREIGN KEY ("flexboxId") REFERENCES "FlexboxCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_gridId_fkey" FOREIGN KEY ("gridId") REFERENCES "GridCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_visualId_fkey" FOREIGN KEY ("visualId") REFERENCES "VisualCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_interactionId_fkey" FOREIGN KEY ("interactionId") REFERENCES "InteractionCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_objectId_fkey" FOREIGN KEY ("objectId") REFERENCES "ObjectCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_animationId_fkey" FOREIGN KEY ("animationId") REFERENCES "AnimationCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "properties" ADD CONSTRAINT "properties_extraId_fkey" FOREIGN KEY ("extraId") REFERENCES "ExtraCss"("id") ON DELETE SET NULL ON UPDATE CASCADE;
