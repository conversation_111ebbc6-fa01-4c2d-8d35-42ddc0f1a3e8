"use server";

import db from "@/lib/db";

export const createText = async (data: any) => {
  try {
    const text = await db.text.create({
      data: {
        sectionItemId: data.sectionId,
        content: data.content,
        rowPosition: data.rowPosition,
      },
      include: {
        sectionItem: {
          include: {
            section: true,
            externalLink: true,
            textImage: true,
          },
        },
      },
    });

    return text;
  } catch (error) {
    console.error("Error creating text:", error);
    return;
  }
};

export const updateText = async (data: any) => {
  try {
    const text = await db.text.update({
      where: {
        id: data.id,
      },
      data: {
        content: data.content,
        rowPosition: data.rowPosition,
      },
      include: {
        sectionItem: {
          include: {
            section: true,
            externalLink: true,
            textImage: true,
          },
        },
      },
    });

    return text;
  } catch (error) {
    console.error("Error updating text:", error);
    return;
  }
};

export const isText = async (id: string) => {
  const text = await db.text.findUnique({
    where: {
      id,
    },
    include: {
      sectionItem: {
        include: {
          section: true,
          externalLink: true,
          textImage: true,
        },
      },
    },
  });

  return text;
};
