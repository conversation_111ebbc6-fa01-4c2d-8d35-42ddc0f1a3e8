import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  try {
    // Clear existing data (optional - remove if you want to keep existing data)
    await prisma.project.deleteMany();
    await prisma.user.deleteMany();

    // Create a user
    const user = await prisma.user.create({
      data: {
        name: "Manish Batra",
        email: "<EMAIL>",
      },
    });

    // Create projects for the user
    const projects = await prisma.project.create({
      data: {
        title: "My First Project",
        userId: user.id,
        sections: {
          create: {
            title: "Section 1",
            index: 0,
            type: "TEXT",
            items: {
              create: {
                is_reverse: false,
                text: {
                  create: {},
                },
              },
            },
          },
        },
      },
      include: {
        sections: {
          include: {
            items: {
              include: {
                text: true,
              },
            },
          },
        },
      },
    });

    // Verify the data was created
    const userWithProjects = await prisma.user.findUnique({
      where: { id: user.id },
      include: { Project: true },
    });

    console.log("User with projects:", userWithProjects);
  } catch (error) {
    console.error("Error during seeding:", error);
    throw error;
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error("Seeding failed:", e);
    await prisma.$disconnect();
    process.exit(1);
  });
