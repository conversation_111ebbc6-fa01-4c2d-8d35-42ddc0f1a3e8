"use server";

import { revalidatePath } from "next/cache";
import {
  updateCssPropertiesQuery,
  getCssPropertiesQuery,
} from "@/queries/css-properties";

export type CssPropertiesData = {
  sectionId: string;
  styles: Record<string, any>;
};

export const updateCssPropertiesAction = async (data: CssPropertiesData) => {
  try {
    const result = await updateCssPropertiesQuery(data);

    if (result) {
      revalidatePath("/");
      return {
        data: result,
        message: "CSS properties updated successfully",
        error: false,
      };
    }

    return {
      data: null,
      message: "Failed to update CSS properties",
      error: true,
    };
  } catch (error) {
    console.error("Error updating CSS properties:", error);
    return {
      data: null,
      message: "An error occurred while updating CSS properties",
      error: true,
    };
  } finally {
    revalidatePath("/");
  }
};

export const getCssPropertiesAction = async (sectionId: string) => {
  try {
    const result = await getCssPropertiesQuery(sectionId);

    return {
      data: result,
      message: "CSS properties retrieved successfully",
      error: false,
    };
  } catch (error) {
    console.error("Error retrieving CSS properties:", error);
    return {
      data: null,
      message: "An error occurred while retrieving CSS properties",
      error: true,
    };
  } finally {
    revalidatePath("/");
  }
};
