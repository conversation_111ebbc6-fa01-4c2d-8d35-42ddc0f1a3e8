"use server";

import db from "@/lib/db";

export const createExternalSectionLink = async (data: any) => {
  try {
    const externalLink = await db.externalLink.create({
      data: {
        sectionItemId: data.sectionId,
        label: data.label,
        url: data.url,
      },
      include: {
        sectionItem: {
          include: {
            section: true,
          },
        },
      },
    });

    return externalLink;
  } catch (error) {
    console.error("Error creating section external link:", error);
    return;
  }
};

export const createExternalTextLink = async (data: any) => {
  try {
    const externalLink = await db.externalLink.create({
      data: {
        textId: data.textId,
        label: data.label,
        url: data.url,
      },
      include: {
        text: {
          include: {
            sectionItem: {
              include: {
                section: true,
              },
            },
          },
        },
      },
    });

    return externalLink;
  } catch (error) {
    console.error("Error creating text external link:", error);
    return;
  }
};

export const updateExternalLink = async (data: any) => {
  try {
    const externalLink = await db.externalLink.update({
      where: {
        id: data.id,
      },
      data: {
        label: data.label,
        url: data.url,
      },
      include: {
        sectionItem: {
          include: {
            section: true,
          },
        },
      },
    });

    return externalLink;
  } catch (error) {
    console.error("Error updating external link:", error);
    return;
  }
};

export const isExternalLink = async (id: string) => {
  const externalLink = await db.externalLink.findUnique({
    where: {
      id,
    },
    include: {
      sectionItem: {
        include: {
          section: true,
        },
      },
    },
  });

  return externalLink;
};

export const deleteExternalLink = async (id: string) => {
  const externalLink = await db.externalLink.delete({
    where: {
      id,
    },
  });

  return externalLink;
};
