-- AlterEnum
ALTER TYPE "SectionType" ADD VALUE 'GALLERY';

-- AlterTable
ALTER TABLE "images" ADD COLUMN     "galleryId" TEXT;

-- CreateTable
CREATE TABLE "videos" (
    "id" TEXT NOT NULL,
    "src" TEXT NOT NULL DEFAULT 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    "alt" TEXT NOT NULL DEFAULT 'Placeholder Video',
    "caption" TEXT,
    "width" TEXT,
    "height" TEXT,
    "sectionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "videos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "text_images" (
    "id" TEXT NOT NULL,
    "textId" TEXT NOT NULL,
    "imageId" TEXT NOT NULL,
    "sectionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "text_images_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "socials" (
    "id" TEXT NOT NULL,
    "sectionId" TEXT NOT NULL,
    "platform" TEXT NOT NULL DEFAULT 'twitter',
    "href" TEXT NOT NULL DEFAULT 'https://twitter.com/viber',
    "icon" TEXT NOT NULL DEFAULT 'https://www.svgrepo.com/show/475437/twitter.svg',
    "label" TEXT NOT NULL DEFAULT 'Twitter',
    "color" TEXT NOT NULL DEFAULT 'blue',
    "size" TEXT NOT NULL DEFAULT '24',
    "position" TEXT NOT NULL DEFAULT 'left',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "socials_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "maps" (
    "id" TEXT NOT NULL,
    "sectionId" TEXT NOT NULL,
    "latitude" DOUBLE PRECISION DEFAULT 0.0,
    "longitude" DOUBLE PRECISION DEFAULT 0.0,
    "zoom" INTEGER DEFAULT 10,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "country" TEXT,
    "pinCode" TEXT,
    "pin" BOOLEAN DEFAULT true,
    "pinColor" TEXT DEFAULT 'red',
    "pinSize" TEXT DEFAULT '24',
    "pinLabel" TEXT,
    "pinIcon" TEXT DEFAULT 'https://www.svgrepo.com/show/475437/twitter.svg',
    "iframe" BOOLEAN DEFAULT true,
    "height" TEXT DEFAULT '400',
    "width" TEXT DEFAULT '100%',
    "style" TEXT,
    "iframeSrc" TEXT,
    "googleApiKey" TEXT,
    "googleMapId" TEXT,
    "googleMapUrl" TEXT,
    "googleMapStyle" TEXT,
    "googleMapType" TEXT DEFAULT 'roadmap',
    "googleMapZoom" INTEGER DEFAULT 10,
    "googleMapCenter" TEXT DEFAULT '0,0',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "maps_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "galleries" (
    "id" TEXT NOT NULL,
    "sectionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "galleries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gallery_images" (
    "id" TEXT NOT NULL,
    "galleryId" TEXT NOT NULL,
    "imageId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "gallery_images_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_GalleryImage" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_GalleryImage_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "videos_sectionId_key" ON "videos"("sectionId");

-- CreateIndex
CREATE INDEX "videos_sectionId_idx" ON "videos"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "text_images_textId_key" ON "text_images"("textId");

-- CreateIndex
CREATE UNIQUE INDEX "text_images_imageId_key" ON "text_images"("imageId");

-- CreateIndex
CREATE UNIQUE INDEX "text_images_sectionId_key" ON "text_images"("sectionId");

-- CreateIndex
CREATE INDEX "text_images_textId_idx" ON "text_images"("textId");

-- CreateIndex
CREATE INDEX "text_images_imageId_idx" ON "text_images"("imageId");

-- CreateIndex
CREATE INDEX "text_images_sectionId_idx" ON "text_images"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "socials_sectionId_key" ON "socials"("sectionId");

-- CreateIndex
CREATE INDEX "socials_sectionId_idx" ON "socials"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "maps_sectionId_key" ON "maps"("sectionId");

-- CreateIndex
CREATE INDEX "maps_sectionId_idx" ON "maps"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "galleries_sectionId_key" ON "galleries"("sectionId");

-- CreateIndex
CREATE INDEX "galleries_sectionId_idx" ON "galleries"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "gallery_images_galleryId_key" ON "gallery_images"("galleryId");

-- CreateIndex
CREATE UNIQUE INDEX "gallery_images_imageId_key" ON "gallery_images"("imageId");

-- CreateIndex
CREATE INDEX "gallery_images_galleryId_idx" ON "gallery_images"("galleryId");

-- CreateIndex
CREATE INDEX "gallery_images_imageId_idx" ON "gallery_images"("imageId");

-- CreateIndex
CREATE INDEX "_GalleryImage_B_index" ON "_GalleryImage"("B");

-- AddForeignKey
ALTER TABLE "images" ADD CONSTRAINT "images_galleryId_fkey" FOREIGN KEY ("galleryId") REFERENCES "galleries"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "videos" ADD CONSTRAINT "videos_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "sections"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "text_images" ADD CONSTRAINT "text_images_textId_fkey" FOREIGN KEY ("textId") REFERENCES "texts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "text_images" ADD CONSTRAINT "text_images_imageId_fkey" FOREIGN KEY ("imageId") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "text_images" ADD CONSTRAINT "text_images_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "sections"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "socials" ADD CONSTRAINT "socials_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "sections"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "maps" ADD CONSTRAINT "maps_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "sections"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "galleries" ADD CONSTRAINT "galleries_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "sections"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gallery_images" ADD CONSTRAINT "gallery_images_galleryId_fkey" FOREIGN KEY ("galleryId") REFERENCES "galleries"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gallery_images" ADD CONSTRAINT "gallery_images_imageId_fkey" FOREIGN KEY ("imageId") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_GalleryImage" ADD CONSTRAINT "_GalleryImage_A_fkey" FOREIGN KEY ("A") REFERENCES "galleries"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_GalleryImage" ADD CONSTRAINT "_GalleryImage_B_fkey" FOREIGN KEY ("B") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;
