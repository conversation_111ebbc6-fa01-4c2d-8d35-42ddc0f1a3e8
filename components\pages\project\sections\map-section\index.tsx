"use client";

import React from "react";
import { Section, Map } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Edit2Icon, MapPin, Navigation } from "lucide-react";
import { cn } from "@/lib/utils";

type Props = {
  section: Section & {
    map?: Map | null;
  };
};

const MapSection = ({ section }: Props) => {
  if (!section.map) {
    return (
      <div className="text-center py-12">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500/10 to-purple-500/10 flex items-center justify-center">
            <MapPin className="w-8 h-8 text-muted-foreground" />
          </div>
          <p className="text-muted-foreground">No map configured</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto group/map-section">
      {/* Modern Map Container */}
      <div className="relative overflow-hidden rounded-2xl shadow-2xl bg-gradient-to-br from-slate-100 via-slate-50 to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-2">
        {/* Gradient border effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl opacity-0 group-hover/map-section:opacity-75 transition-opacity duration-500 blur-sm"></div>
        
        {/* Map content */}
        <div className="relative bg-background rounded-xl overflow-hidden">
          {/* Edit button overlay */}
          <div className="absolute top-4 right-4 z-10 opacity-0 group-hover/map-section:opacity-100 transition-all duration-300 transform translate-y-2 group-hover/map-section:translate-y-0">
            <Button
              variant="secondary"
              size="sm"
              className="backdrop-blur-md bg-white/10 border-white/20 text-white hover:bg-white/20 shadow-lg gap-2"
            >
              <Edit2Icon className="w-4 h-4" />
              Edit Map
            </Button>
          </div>

          {/* Map iframe or placeholder */}
          <div className="relative aspect-video w-full bg-gradient-to-br from-muted/20 to-muted/40">
            {section.map.embedUrl ? (
              <iframe
                src={section.map.embedUrl}
                width="100%"
                height="100%"
                className="absolute inset-0 w-full h-full border-none"
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center mx-auto">
                    <Navigation className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">
                      {section.map.title || "Interactive Map"}
                    </h3>
                    <p className="text-muted-foreground">
                      {section.map.address || "Map location will be displayed here"}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Map details */}
      <div className="mt-6 text-center space-y-2">
        {section.map.title && (
          <h3 className="text-xl font-semibold text-foreground">
            {section.map.title}
          </h3>
        )}
        {section.map.address && (
          <p className="text-muted-foreground leading-relaxed max-w-2xl mx-auto flex items-center justify-center gap-2">
            <MapPin className="w-4 h-4" />
            {section.map.address}
          </p>
        )}
        {section.map.description && (
          <p className="text-muted-foreground leading-relaxed max-w-2xl mx-auto">
            {section.map.description}
          </p>
        )}
      </div>
    </div>
  );
};

export default MapSection;
