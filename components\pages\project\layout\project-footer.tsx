import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Save, Undo2, Redo2, Palette, Layout } from "lucide-react";

type Props = {};

const ProjectFooter = ({}: Props) => {
  return (
    <footer className="fixed bottom-0 left-0 right-0 z-40 bg-background/95 backdrop-blur-md border-t border-border/50">
      <div className="container mx-auto px-8 py-3">
        <div className="flex items-center justify-between">
          {/* Left side - Quick actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="gap-2 text-muted-foreground hover:text-foreground"
            >
              <Undo2 className="h-4 w-4" />
              Undo
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="gap-2 text-muted-foreground hover:text-foreground"
            >
              <Redo2 className="h-4 w-4" />
              Redo
            </Button>
            <div className="h-4 w-px bg-border mx-2" />
            <Button
              variant="ghost"
              size="sm"
              className="gap-2 text-muted-foreground hover:text-foreground"
            >
              <Palette className="h-4 w-4" />
              Theme
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="gap-2 text-muted-foreground hover:text-foreground"
            >
              <Layout className="h-4 w-4" />
              Layout
            </Button>
          </div>

          {/* Center - Status */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
              Auto-saved
            </div>
          </div>

          {/* Right side - Save button */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="gap-2">
              <Save className="h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default ProjectFooter;
