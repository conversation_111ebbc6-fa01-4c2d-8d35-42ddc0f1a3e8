"use client";

import React from "react";
import { Section } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Edit2Icon, Send, User, Mail, MessageSquare } from "lucide-react";
import { cn } from "@/lib/utils";

type Props = {
  section: Section & {
    form?: any | null; // Form type not defined in schema yet
  };
};

const FormSection = ({ section }: Props) => {
  return (
    <div className="w-full max-w-2xl mx-auto group/form-section">
      {/* Modern Form Container */}
      <div className="relative overflow-hidden rounded-2xl shadow-2xl bg-gradient-to-br from-background via-background to-muted/10 p-1">
        {/* Gradient border effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl opacity-0 group-hover/form-section:opacity-75 transition-opacity duration-500 blur-sm"></div>
        
        {/* Form content */}
        <div className="relative bg-background rounded-xl p-8">
          {/* Edit button overlay */}
          <div className="absolute top-4 right-4 z-10 opacity-0 group-hover/form-section:opacity-100 transition-all duration-300 transform translate-y-2 group-hover/form-section:translate-y-0">
            <Button
              variant="secondary"
              size="sm"
              className="backdrop-blur-md bg-muted/50 border-border/50 hover:bg-muted/70 shadow-lg gap-2"
            >
              <Edit2Icon className="w-4 h-4" />
              Edit Form
            </Button>
          </div>

          {/* Form header */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-foreground mb-2">
              Get In Touch
            </h2>
            <p className="text-muted-foreground">
              We'd love to hear from you. Send us a message and we'll respond as soon as possible.
            </p>
          </div>

          {/* Form fields */}
          <form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-semibold text-foreground flex items-center gap-2">
                  <User className="w-4 h-4" />
                  Full Name
                </Label>
                <Input
                  id="name"
                  placeholder="Enter your full name"
                  className="border-2 border-border/50 focus:border-primary/50 rounded-lg transition-colors"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-semibold text-foreground flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email address"
                  className="border-2 border-border/50 focus:border-primary/50 rounded-lg transition-colors"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="subject" className="text-sm font-semibold text-foreground">
                Subject
              </Label>
              <Input
                id="subject"
                placeholder="What's this about?"
                className="border-2 border-border/50 focus:border-primary/50 rounded-lg transition-colors"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="message" className="text-sm font-semibold text-foreground flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                Message
              </Label>
              <Textarea
                id="message"
                placeholder="Tell us more about your inquiry..."
                className="min-h-[120px] border-2 border-border/50 focus:border-primary/50 rounded-lg transition-colors resize-none"
              />
            </div>
            
            <Button 
              type="submit" 
              className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2"
            >
              <Send className="w-4 h-4" />
              Send Message
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default FormSection;
