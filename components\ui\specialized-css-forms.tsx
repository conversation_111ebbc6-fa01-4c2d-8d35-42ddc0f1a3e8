"use client";

import React from "react";
import { GenericCssPropertiesForm } from "./css-properties-form";
import { Settings, Image, Video, MousePointer, FileText } from "lucide-react";
import { CssPropertyConfig } from "@/lib/types";
import {
  BUTTON_CSS_PROPERTIES,
  IMAGE_CSS_PROPERTIES,
  TEXT_CSS_PROPERTIES,
} from "@/lib/css-form-static";

// Specialized form components
interface SpecializedFormProps {
  entityId: string;
  onSave: (styles: Record<string, any>) => Promise<void>;
  onLoad?: () => Promise<Record<string, any> | null>;
  children?: React.ReactNode;
  className?: string;
}

export const ImageCssPropertiesForm: React.FC<SpecializedFormProps> = (
  props
) => (
  <GenericCssPropertiesForm
    {...props}
    title="Image CSS Properties"
    description="Customize the styling properties for this image"
    entityType="image"
    propertyConfigs={IMAGE_CSS_PROPERTIES}
  >
    {props.children || (
      <button className="flex items-center gap-1 px-2 py-1 text-xs bg-white/10 hover:bg-white/20 rounded transition-colors">
        <Image className="h-3 w-3" />
        Style
      </button>
    )}
  </GenericCssPropertiesForm>
);

export const ButtonCssPropertiesForm: React.FC<SpecializedFormProps> = (
  props
) => (
  <GenericCssPropertiesForm
    {...props}
    title="Button CSS Properties"
    description="Customize the styling properties for this button"
    entityType="button"
    propertyConfigs={BUTTON_CSS_PROPERTIES}
  >
    {props.children || (
      <button className="flex items-center gap-1 px-2 py-1 text-xs bg-white/10 hover:bg-white/20 rounded transition-colors">
        <MousePointer className="h-3 w-3" />
        Style
      </button>
    )}
  </GenericCssPropertiesForm>
);

export const TextCssPropertiesForm: React.FC<SpecializedFormProps> = (
  props
) => (
  <GenericCssPropertiesForm
    {...props}
    title="Text CSS Properties"
    description="Customize the styling properties for this text"
    entityType="text"
    propertyConfigs={TEXT_CSS_PROPERTIES}
  >
    {props.children || (
      <button className="flex items-center gap-1 px-2 py-1 text-xs bg-white/10 hover:bg-white/20 rounded transition-colors">
        <FileText className="h-3 w-3" />
        Style
      </button>
    )}
  </GenericCssPropertiesForm>
);

export const VideoCssPropertiesForm: React.FC<SpecializedFormProps> = (
  props
) => (
  <GenericCssPropertiesForm
    {...props}
    title="Video CSS Properties"
    description="Customize the styling properties for this video"
    entityType="video"
    propertyConfigs={IMAGE_CSS_PROPERTIES} // Videos use similar properties to images
  >
    {props.children || (
      <button className="flex items-center gap-1 px-2 py-1 text-xs bg-white/10 hover:bg-white/20 rounded transition-colors">
        <Video className="h-3 w-3" />
        Style
      </button>
    )}
  </GenericCssPropertiesForm>
);
