import React from "react";
import { Section } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Move } from "lucide-react";
import { useDragControls } from "framer-motion";

type Props = {
  section: Section;
};

const DragSection = (props: Props) => {
  const dragControls = useDragControls();

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-primary/10 cursor-grab active:cursor-grabbing transition-colors duration-200"
        title="Drag to reorder sections"
        onPointerDown={(e) => dragControls.start(e)}
      >
        <Move className="h-4 w-4" />
      </Button>
    </>
  );
};

export default DragSection;
