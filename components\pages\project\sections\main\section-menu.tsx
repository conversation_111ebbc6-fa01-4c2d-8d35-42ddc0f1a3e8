"use client";

import React from "react";
import { cn } from "@/lib/utils";
import DeleteSection from "../section-menus/delete-section";
import { Section, Text, ExternalLink } from "@prisma/client";
import ExternalLinks from "../section-menus/external-link";
import DragSection from "../section-menus/drag-section";
import SectionSettings from "../section-menus/settings";
import CopySection from "../section-menus/copy-section";
import EditSection from "../section-menus/edit-section";
import ReversableSection from "../section-menus/reversable-section";

type Props = {
  section: Section & {
    text?: (Text & { externalLink?: ExternalLink | null }) | null;
    externalLink?: ExternalLink | null;
  };
};

const SectionMenu = ({ section }: Props) => {
  return (
    <div
      className={cn(
        "absolute top-4 right-4 opacity-0 group-hover/section:opacity-100",
        "transition-all duration-500 transform translate-x-4 translate-y-2 group-hover/section:translate-x-0 group-hover/section:translate-y-0",
        "bg-background/95 backdrop-blur-md border border-border/50 rounded-xl p-2 shadow-2xl",
        "flex items-center gap-1 z-30",
        "before:absolute before:inset-0 before:bg-gradient-to-r before:from-blue-500/10 before:to-purple-500/10 before:rounded-xl before:-z-10 before:opacity-0 before:group-hover/section:opacity-100 before:transition-opacity before:duration-500"
      )}
    >
      {/* Move/Drag handle */}
      <DragSection section={section} />

      {/* Settings */}
      <SectionSettings section={section} />

      {/* Duplicate */}
      <CopySection section={section} />

      {/* External Link */}
      {section.type === "TEXT" && <ExternalLinks section={section} />}

      {/* Reversable  */}
      {section.type === "TEXTIMAGE" && <ReversableSection section={section} />}

      {/* Delete */}
      <DeleteSection section={section} />
    </div>
  );
};

export default SectionMenu;
