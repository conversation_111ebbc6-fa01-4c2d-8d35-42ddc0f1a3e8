"use server";

import {
  createSection,
  deleteSection,
  isSection,
  updateSectionIsReverse,
  updateSectionTitle,
  updateSectionOrder,
  isSectionItem,
} from "@/queries/sections";
import { ActionState, StatusCode } from "@/lib/types";
import { Section, SectionItem, Video } from "@prisma/client";
import { revalidatePath } from "next/cache";

type CreateSectionProps = {
  projectId: Section["projectId"];
  index: Section["index"];
  type: Section["type"];
  src?: Video["src"];
};

export const createSectionAction = async ({
  projectId,
  index,
  type,
  src,
}: CreateSectionProps): Promise<
  ActionState<(Section & { items: SectionItem | null }) | null>
> => {
  try {
    const section = await createSection({
      projectId,
      index,
      type,
      src,
    });

    return {
      code: StatusCode.Created,
      message: "Section created successfully",
      data: section as any,
      success: true,
    };
  } catch (error) {
    console.error("Error creating section:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the section",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${projectId}`);
  }
};

//delete setion action

export type DeleteSectionProps = Pick<Section, "id" | "projectId" | "index">;

export const deleteSectionAction = async (
  data: DeleteSectionProps
): Promise<ActionState<Section>> => {
  try {
    const sectionExists = await isSection(data.id);

    if (!sectionExists) {
      return {
        code: StatusCode.NotFound,
        message: "Section not found",
        success: false,
      };
    }

    const section = await deleteSection(data);

    return {
      code: StatusCode.Ok,
      message: "Section deleted successfully",
      data: section,
      success: true,
    };
  } catch (error) {
    console.error("Error deleting section:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while deleting the section",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${data.projectId}`);
  }
};

export type UpdateSectionTitleProps = Pick<Section, "id" | "title">;

export const updateSectionTitleAction = async ({
  id,
  title,
}: UpdateSectionTitleProps): Promise<ActionState<Section>> => {
  let sectionExists;
  try {
    sectionExists = await isSection(id);

    if (!sectionExists) {
      return {
        code: StatusCode.NotFound,
        message: "Section not found",
        success: false,
      };
    }
    const section = await updateSectionTitle({
      id,
      title,
    });

    return {
      code: StatusCode.Ok,
      message: "Section title updated successfully",
      data: section,
      success: true,
    };
  } catch (error) {
    console.error("Error updating section title:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the section title",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${sectionExists?.projectId}`);
  }
};

export type UpdateSectionProps = Pick<SectionItem, "id" | "is_reverse">;

export const updateSectionAction = async ({
  id,
  is_reverse,
}: UpdateSectionProps): Promise<ActionState<SectionItem>> => {
  let sectionExists;
  try {
    sectionExists = await isSectionItem(id);

    if (!sectionExists) {
      return {
        code: StatusCode.NotFound,
        message: "Section not found",
        success: false,
      };
    }

    const section = await updateSectionIsReverse({
      id,
      is_reverse,
    });

    return {
      code: StatusCode.Ok,
      message: "Section updated successfully",
      data: section,
      success: true,
    };
  } catch (error) {
    console.error("Error updating section:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the section",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${sectionExists?.section?.projectId}`);
  }
};

export type UpdateSectionOrderProps = {
  projectId: Section["projectId"];
  sectionOrders: { id: string; index: number }[];
};

export const updateSectionOrderAction = async ({
  projectId,
  sectionOrders,
}: UpdateSectionOrderProps): Promise<ActionState<Section[]>> => {
  try {
    const sections = await updateSectionOrder({
      projectId,
      sectionOrders,
    });

    if (!sections) {
      return {
        code: StatusCode.InternalServerError,
        message: "Failed to update section order",
        success: false,
      };
    }

    return {
      code: StatusCode.Ok,
      message: "Section order updated successfully",
      data: sections,
      success: true,
    };
  } catch (error) {
    console.error("Error updating section order:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the section order",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${projectId}`);
  }
};
