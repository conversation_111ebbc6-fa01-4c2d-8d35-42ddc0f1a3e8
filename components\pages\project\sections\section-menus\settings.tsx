import React from "react";
import { Section } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Settings } from "lucide-react";
import SectionCssPropertiesForm from "./section-css-properties-form";

type Props = {
  section: Section;
};

const SectionSettings = ({ section }: Props) => {
  return (
    <SectionCssPropertiesForm section={section}>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="Section CSS Settings"
      >
        <Settings className="h-4 w-4" />
      </Button>
    </SectionCssPropertiesForm>
  );
};

export default SectionSettings;
