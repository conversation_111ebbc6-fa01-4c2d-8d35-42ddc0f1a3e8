import { deleteSectionAction, DeleteSectionProps } from "@/actions/section";
import DeleteDialog from "@/components/core/delete-dialog";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import React from "react";

type Props = {
  section: DeleteSectionProps;
};

const DeleteSection = ({ section }: Props) => {
  return (
    <>
      <DeleteDialog action={deleteSectionAction.bind(null, section)}>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          title="Delete section"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </DeleteDialog>
    </>
  );
};

export default DeleteSection;
