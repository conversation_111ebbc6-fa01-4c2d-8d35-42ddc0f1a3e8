"use server";

import db from "@/lib/db";

export const createImage = async (data: any) => {
  try {
    const image = await db.image.create({
      data: {
        sectionItemId: data.sectionId,
        src: data.src,
        alt: data.alt,
        caption: data.caption,
        width: data.width,
        height: data.height,
      },
      include: {
        sectionItem: {
          include: {
            section: true,
          },
        },
      },
    });

    return image;
  } catch (error) {
    console.error("Error creating image:", error);
    return;
  }
};

export const updateImage = async (data: any) => {
  try {
    const image = await db.image.update({
      where: {
        id: data.id,
      },
      data: {
        src: data.src,
        alt: data.alt,
        caption: data.caption,
        width: data.width,
        height: data.height,
      },
      include: {
        sectionItem: {
          include: {
            section: true,
          },
        },
      },
    });

    return image;
  } catch (error) {
    console.error("Error updating image:", error);
    return;
  }
};

export const isImage = async (id: string) => {
  const image = await db.image.findUnique({
    where: {
      id,
    },
    include: {
      sectionItem: {
        include: {
          section: true,
        },
      },
    },
  });

  return image;
};
