"use client";

import React from "react";
import { Section } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit2Icon, ShoppingCart, Star, Heart } from "lucide-react";
import { cn } from "@/lib/utils";

type Props = {
  section: Section & {
    ecommerce?: any | null; // Ecommerce type not defined in schema yet
  };
};

const EcommerceSection = ({ section }: Props) => {
  // Mock product data for demonstration
  const products = [
    {
      id: 1,
      name: "Premium Wireless Headphones",
      price: 299.99,
      originalPrice: 399.99,
      image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop",
      rating: 4.8,
      reviews: 124,
      badge: "Best Seller"
    },
    {
      id: 2,
      name: "Smart Fitness Watch",
      price: 199.99,
      originalPrice: null,
      image: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop",
      rating: 4.6,
      reviews: 89,
      badge: "New"
    },
    {
      id: 3,
      name: "Portable Bluetooth Speaker",
      price: 79.99,
      originalPrice: 99.99,
      image: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400&h=400&fit=crop",
      rating: 4.7,
      reviews: 156,
      badge: "Sale"
    }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto group/ecommerce-section">
      {/* Edit button */}
      <div className="absolute top-4 right-4 z-10 opacity-0 group-hover/ecommerce-section:opacity-100 transition-all duration-300 transform translate-y-2 group-hover/ecommerce-section:translate-y-0">
        <Button
          variant="secondary"
          size="sm"
          className="backdrop-blur-md bg-muted/50 border-border/50 hover:bg-muted/70 shadow-lg gap-2"
        >
          <Edit2Icon className="w-4 h-4" />
          Edit Products
        </Button>
      </div>

      {/* Section header */}
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-foreground mb-4">
          Featured Products
        </h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Discover our carefully curated selection of premium products designed to enhance your lifestyle.
        </p>
      </div>

      {/* Products grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {products.map((product) => (
          <div
            key={product.id}
            className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-background via-background to-muted/10 p-1 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
          >
            {/* Gradient border effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl opacity-0 group-hover:opacity-75 transition-opacity duration-500 blur-sm"></div>
            
            {/* Product card */}
            <div className="relative bg-background rounded-xl overflow-hidden">
              {/* Product badge */}
              {product.badge && (
                <Badge 
                  className={cn(
                    "absolute top-4 left-4 z-10",
                    product.badge === "Sale" && "bg-red-500 hover:bg-red-600",
                    product.badge === "New" && "bg-green-500 hover:bg-green-600",
                    product.badge === "Best Seller" && "bg-blue-500 hover:bg-blue-600"
                  )}
                >
                  {product.badge}
                </Badge>
              )}
              
              {/* Wishlist button */}
              <Button
                variant="ghost"
                size="sm"
                className="absolute top-4 right-4 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 w-8 h-8 p-0 bg-white/80 hover:bg-white"
              >
                <Heart className="w-4 h-4" />
              </Button>

              {/* Product image */}
              <div className="aspect-square overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
              </div>

              {/* Product details */}
              <div className="p-6 space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    {product.name}
                  </h3>
                  
                  {/* Rating */}
                  <div className="flex items-center gap-2 mb-3">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={cn(
                            "w-4 h-4",
                            i < Math.floor(product.rating)
                              ? "fill-yellow-400 text-yellow-400"
                              : "text-muted-foreground"
                          )}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {product.rating} ({product.reviews})
                    </span>
                  </div>
                  
                  {/* Price */}
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-foreground">
                      ${product.price}
                    </span>
                    {product.originalPrice && (
                      <span className="text-lg text-muted-foreground line-through">
                        ${product.originalPrice}
                      </span>
                    )}
                  </div>
                </div>
                
                {/* Add to cart button */}
                <Button 
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <ShoppingCart className="w-4 h-4" />
                  Add to Cart
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EcommerceSection;
