import React from "react";
import { Section, SectionItem, Video } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Edit2Icon } from "lucide-react";
import VideoFormDialog from "./video-form-dialog";

type Props = {
  section: Section & {
    items: (SectionItem & { video: Video | null })[];
  };
};

const VideoSection = ({ section }: Props) => {
  // Get the first item from the items array
  const firstItem = section.items?.[0];
  if (!firstItem?.video) {
    return null;
  }

  return (
    <div className="relative w-full max-w-4xl mx-auto group/section">
      {/* Video Container with Modern Styling */}
      <div className="relative overflow-hidden rounded-2xl shadow-2xl bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-1">
        {/* Gradient Border Effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl opacity-75 blur-sm group-hover/section:opacity-100 transition-opacity duration-500"></div>

        {/* Video Content */}
        <div className="relative bg-black rounded-xl overflow-hidden">
          {/* Edit Button Overlay */}
          <VideoFormDialog sectionId={firstItem.id} video={firstItem.video}>
            <div className="absolute top-4 right-4 z-10 opacity-0 group-hover/section:opacity-100 transition-all duration-300 transform translate-y-2 group-hover/section:translate-y-0">
              <Button
                variant="secondary"
                size="sm"
                className="backdrop-blur-md bg-white/10 border-white/20 text-white hover:bg-white/20 shadow-lg gap-2"
              >
                <Edit2Icon className="w-4 h-4" />
                Edit Video
              </Button>
            </div>
          </VideoFormDialog>

          {/* Video Player */}
          <div className="relative aspect-video w-full">
            <iframe
              width="100%"
              height="100%"
              src={firstItem.video?.src as string}
              className="absolute inset-0 w-full h-full"
              style={{ border: "none" }}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerPolicy="strict-origin-when-cross-origin"
              allowFullScreen
            />
          </div>
        </div>
      </div>

      {/* Video Title and Caption */}
      <div className="mt-6 text-center space-y-2">
        {firstItem.video?.alt && (
          <h3 className="text-xl font-semibold text-foreground">
            {firstItem.video.alt}
          </h3>
        )}
        {firstItem.video?.caption && (
          <p className="text-muted-foreground leading-relaxed max-w-2xl mx-auto">
            {firstItem.video.caption}
          </p>
        )}
      </div>
    </div>
  );
};

export default VideoSection;
