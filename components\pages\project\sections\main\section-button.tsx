import React from "react";
import SectionOptionDialog from "./section-option-dialog";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { $Enums, Section } from "@prisma/client";

type Props = {
  index: Section["index"];
  projectId: Section["projectId"];
  sectionType?: $Enums.SectionType;
  sectionId?: Section["id"];
};

const SectionButton = ({ ...rest }: Props) => {
  return (
    <div className="relative group/section-btn">
      {/* Hover line indicator */}
      <div className="h-px bg-border group-hover/section-btn:bg-gradient-to-r group-hover/section-btn:from-blue-500 group-hover/section-btn:to-purple-500 transition-all duration-300" />

      {/* Add section button */}
      <SectionOptionDialog {...rest}>
        <Button
          className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 h-8 w-8 rounded-full bg-background border-2 border-border hover:border-primary shadow-sm hover:shadow-md transition-all duration-200 group-hover/section-btn:scale-110 group-hover/section-btn:bg-gradient-to-r group-hover/section-btn:from-blue-500 group-hover/section-btn:to-purple-500 group-hover/section-btn:border-transparent group-hover/section-btn:text-white"
          size="icon"
          variant="outline"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </SectionOptionDialog>

      {/* Tooltip on hover */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-12 opacity-0 group-hover/section-btn:opacity-100 transition-opacity duration-200 pointer-events-none">
        <div className="bg-foreground text-background text-xs px-2 py-1 rounded whitespace-nowrap">
          Add Section
        </div>
      </div>
    </div>
  );
};

export default SectionButton;
