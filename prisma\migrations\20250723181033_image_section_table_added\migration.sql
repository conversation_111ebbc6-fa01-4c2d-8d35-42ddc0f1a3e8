-- CreateTable
CREATE TABLE "images" (
    "id" TEXT NOT NULL,
    "src" TEXT NOT NULL DEFAULT 'https://images.unsplash.com/photo-1702834000621-76c4a9d15868?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    "alt" TEXT NOT NULL DEFAULT 'Placeholder Image',
    "caption" TEXT,
    "sectionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "images_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "images_sectionId_key" ON "images"("sectionId");

-- CreateIndex
CREATE INDEX "images_sectionId_idx" ON "images"("sectionId");

-- AddForeignKey
ALTER TABLE "images" ADD CONSTRAINT "images_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "sections"("id") ON DELETE CASCADE ON UPDATE CASCADE;
