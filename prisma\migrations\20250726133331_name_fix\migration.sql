/*
  Warnings:

  - You are about to drop the column `section_item_id` on the `ecommerces` table. All the data in the column will be lost.
  - You are about to drop the column `section_item_id` on the `external_links` table. All the data in the column will be lost.
  - You are about to drop the column `section_item_id` on the `forms` table. All the data in the column will be lost.
  - You are about to drop the column `section_item_id` on the `galleries` table. All the data in the column will be lost.
  - You are about to drop the column `section_item_id` on the `images` table. All the data in the column will be lost.
  - You are about to drop the column `section_item_id` on the `maps` table. All the data in the column will be lost.
  - You are about to drop the column `section_item_id` on the `text_images` table. All the data in the column will be lost.
  - You are about to drop the column `section_item_id` on the `texts` table. All the data in the column will be lost.
  - You are about to drop the column `section_item_id` on the `videos` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[sectionItemId]` on the table `ecommerces` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[sectionItemId]` on the table `external_links` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[sectionItemId]` on the table `forms` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[sectionItemId]` on the table `galleries` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[sectionItemId]` on the table `images` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[sectionItemId]` on the table `maps` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[sectionItemId]` on the table `text_images` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[sectionItemId]` on the table `texts` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[sectionItemId]` on the table `videos` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "ecommerces" DROP CONSTRAINT "ecommerces_section_item_id_fkey";

-- DropForeignKey
ALTER TABLE "external_links" DROP CONSTRAINT "external_links_section_item_id_fkey";

-- DropForeignKey
ALTER TABLE "forms" DROP CONSTRAINT "forms_section_item_id_fkey";

-- DropForeignKey
ALTER TABLE "galleries" DROP CONSTRAINT "galleries_section_item_id_fkey";

-- DropForeignKey
ALTER TABLE "images" DROP CONSTRAINT "images_section_item_id_fkey";

-- DropForeignKey
ALTER TABLE "maps" DROP CONSTRAINT "maps_section_item_id_fkey";

-- DropForeignKey
ALTER TABLE "text_images" DROP CONSTRAINT "text_images_section_item_id_fkey";

-- DropForeignKey
ALTER TABLE "texts" DROP CONSTRAINT "texts_section_item_id_fkey";

-- DropForeignKey
ALTER TABLE "videos" DROP CONSTRAINT "videos_section_item_id_fkey";

-- DropIndex
DROP INDEX "ecommerces_section_item_id_idx";

-- DropIndex
DROP INDEX "ecommerces_section_item_id_key";

-- DropIndex
DROP INDEX "external_links_section_item_id_key";

-- DropIndex
DROP INDEX "forms_section_item_id_idx";

-- DropIndex
DROP INDEX "forms_section_item_id_key";

-- DropIndex
DROP INDEX "galleries_section_item_id_idx";

-- DropIndex
DROP INDEX "galleries_section_item_id_key";

-- DropIndex
DROP INDEX "images_section_item_id_idx";

-- DropIndex
DROP INDEX "images_section_item_id_key";

-- DropIndex
DROP INDEX "maps_section_item_id_idx";

-- DropIndex
DROP INDEX "maps_section_item_id_key";

-- DropIndex
DROP INDEX "text_images_section_item_id_idx";

-- DropIndex
DROP INDEX "text_images_section_item_id_key";

-- DropIndex
DROP INDEX "texts_section_item_id_idx";

-- DropIndex
DROP INDEX "texts_section_item_id_key";

-- DropIndex
DROP INDEX "videos_section_item_id_idx";

-- DropIndex
DROP INDEX "videos_section_item_id_key";

-- AlterTable
ALTER TABLE "ecommerces" DROP COLUMN "section_item_id",
ADD COLUMN     "sectionItemId" TEXT;

-- AlterTable
ALTER TABLE "external_links" DROP COLUMN "section_item_id",
ADD COLUMN     "sectionItemId" TEXT;

-- AlterTable
ALTER TABLE "forms" DROP COLUMN "section_item_id",
ADD COLUMN     "sectionItemId" TEXT;

-- AlterTable
ALTER TABLE "galleries" DROP COLUMN "section_item_id",
ADD COLUMN     "sectionItemId" TEXT;

-- AlterTable
ALTER TABLE "images" DROP COLUMN "section_item_id",
ADD COLUMN     "sectionItemId" TEXT;

-- AlterTable
ALTER TABLE "maps" DROP COLUMN "section_item_id",
ADD COLUMN     "sectionItemId" TEXT;

-- AlterTable
ALTER TABLE "text_images" DROP COLUMN "section_item_id",
ADD COLUMN     "sectionItemId" TEXT;

-- AlterTable
ALTER TABLE "texts" DROP COLUMN "section_item_id",
ADD COLUMN     "sectionItemId" TEXT;

-- AlterTable
ALTER TABLE "videos" DROP COLUMN "section_item_id",
ADD COLUMN     "sectionItemId" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "ecommerces_sectionItemId_key" ON "ecommerces"("sectionItemId");

-- CreateIndex
CREATE INDEX "ecommerces_sectionItemId_idx" ON "ecommerces"("sectionItemId");

-- CreateIndex
CREATE UNIQUE INDEX "external_links_sectionItemId_key" ON "external_links"("sectionItemId");

-- CreateIndex
CREATE UNIQUE INDEX "forms_sectionItemId_key" ON "forms"("sectionItemId");

-- CreateIndex
CREATE INDEX "forms_sectionItemId_idx" ON "forms"("sectionItemId");

-- CreateIndex
CREATE UNIQUE INDEX "galleries_sectionItemId_key" ON "galleries"("sectionItemId");

-- CreateIndex
CREATE INDEX "galleries_sectionItemId_idx" ON "galleries"("sectionItemId");

-- CreateIndex
CREATE UNIQUE INDEX "images_sectionItemId_key" ON "images"("sectionItemId");

-- CreateIndex
CREATE INDEX "images_sectionItemId_idx" ON "images"("sectionItemId");

-- CreateIndex
CREATE UNIQUE INDEX "maps_sectionItemId_key" ON "maps"("sectionItemId");

-- CreateIndex
CREATE INDEX "maps_sectionItemId_idx" ON "maps"("sectionItemId");

-- CreateIndex
CREATE UNIQUE INDEX "text_images_sectionItemId_key" ON "text_images"("sectionItemId");

-- CreateIndex
CREATE INDEX "text_images_sectionItemId_idx" ON "text_images"("sectionItemId");

-- CreateIndex
CREATE UNIQUE INDEX "texts_sectionItemId_key" ON "texts"("sectionItemId");

-- CreateIndex
CREATE INDEX "texts_sectionItemId_idx" ON "texts"("sectionItemId");

-- CreateIndex
CREATE UNIQUE INDEX "videos_sectionItemId_key" ON "videos"("sectionItemId");

-- CreateIndex
CREATE INDEX "videos_sectionItemId_idx" ON "videos"("sectionItemId");

-- AddForeignKey
ALTER TABLE "texts" ADD CONSTRAINT "texts_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "external_links" ADD CONSTRAINT "external_links_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "images" ADD CONSTRAINT "images_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "videos" ADD CONSTRAINT "videos_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "text_images" ADD CONSTRAINT "text_images_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "maps" ADD CONSTRAINT "maps_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "galleries" ADD CONSTRAINT "galleries_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forms" ADD CONSTRAINT "forms_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ecommerces" ADD CONSTRAINT "ecommerces_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;
