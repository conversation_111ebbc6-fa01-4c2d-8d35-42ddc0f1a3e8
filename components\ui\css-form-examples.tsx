"use client";

import React from "react";
import { GenericCssPropertiesForm, CssPropertyConfig } from "./css-properties-form";
import { 
  ImageCssPropertiesForm, 
  ButtonCssPropertiesForm, 
  TextCssPropertiesForm, 
  VideoCssPropertiesForm 
} from "./specialized-css-forms";
import SectionCssPropertiesForm from "../pages/project/sections/section-menus/section-css-properties-form";

// Example: Custom form for a specific use case
const CARD_CSS_PROPERTIES: CssPropertyConfig[] = [
  // Layout
  { key: "display", label: "Display", type: "select", category: "Layout", 
    options: [
      { value: "default", label: "Default" },
      { value: "block", label: "Block" },
      { value: "flex", label: "Flex" }
    ]
  },
  { key: "width", label: "Width", type: "input", category: "Layout", placeholder: "300px, 100%" },
  { key: "height", label: "Height", type: "input", category: "Layout", placeholder: "auto, 200px" },
  
  // Card Styling
  { key: "backgroundColor", label: "Background Color", type: "input", category: "Card Styling", placeholder: "#ffffff" },
  { key: "borderRadius", label: "Border Radius", type: "input", category: "Card Styling", placeholder: "8px, 1rem" },
  { key: "boxShadow", label: "Box Shadow", type: "input", category: "Card Styling", placeholder: "0 4px 6px rgba(0,0,0,0.1)" },
  { key: "border", label: "Border", type: "input", category: "Card Styling", placeholder: "1px solid #e5e7eb" },
  
  // Spacing
  { key: "padding", label: "Padding", type: "input", category: "Spacing", placeholder: "1rem, 16px" },
  { key: "margin", label: "Margin", type: "input", category: "Spacing", placeholder: "1rem 0" },
  
  // Effects
  { key: "transition", label: "Transition", type: "input", category: "Effects", placeholder: "all 0.2s ease" },
  { key: "transform", label: "Transform (Hover)", type: "input", category: "Effects", placeholder: "translateY(-2px)" },
];

// Example usage components
export const CssFormExamples = () => {
  // Mock save/load functions for examples
  const mockSave = async (styles: Record<string, any>) => {
    console.log("Saving styles:", styles);
    // In real implementation, this would save to your backend
  };

  const mockLoad = async () => {
    console.log("Loading styles...");
    // In real implementation, this would load from your backend
    return { backgroundColor: "#f3f4f6", padding: "1rem" };
  };

  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold">CSS Properties Form Examples</h2>
      
      {/* Generic Form Example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">1. Generic CSS Properties Form</h3>
        <p className="text-gray-600">
          The most flexible option - includes all common CSS properties organized by category.
        </p>
        <GenericCssPropertiesForm
          title="Generic CSS Properties"
          description="Full set of CSS properties for maximum flexibility"
          entityId="example-1"
          entityType="generic"
          onSave={mockSave}
          onLoad={mockLoad}
        />
      </div>

      {/* Section Form Example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">2. Section CSS Properties Form</h3>
        <p className="text-gray-600">
          Specialized for sections - uses the generic form with section-specific save/load logic.
        </p>
        <SectionCssPropertiesForm section={{ id: "section-1" } as any} />
      </div>

      {/* Image Form Example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">3. Image CSS Properties Form</h3>
        <p className="text-gray-600">
          Optimized for images - includes sizing, object-fit, filters, and image-specific properties.
        </p>
        <ImageCssPropertiesForm
          entityId="image-1"
          onSave={mockSave}
          onLoad={mockLoad}
        />
      </div>

      {/* Button Form Example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">4. Button CSS Properties Form</h3>
        <p className="text-gray-600">
          Tailored for buttons - focuses on typography, colors, borders, and interactive states.
        </p>
        <ButtonCssPropertiesForm
          entityId="button-1"
          onSave={mockSave}
          onLoad={mockLoad}
        />
      </div>

      {/* Text Form Example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">5. Text CSS Properties Form</h3>
        <p className="text-gray-600">
          Perfect for text elements - emphasizes typography, spacing, and text effects.
        </p>
        <TextCssPropertiesForm
          entityId="text-1"
          onSave={mockSave}
          onLoad={mockLoad}
        />
      </div>

      {/* Video Form Example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">6. Video CSS Properties Form</h3>
        <p className="text-gray-600">
          Similar to images but optimized for video elements.
        </p>
        <VideoCssPropertiesForm
          entityId="video-1"
          onSave={mockSave}
          onLoad={mockLoad}
        />
      </div>

      {/* Custom Card Form Example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">7. Custom Card CSS Properties Form</h3>
        <p className="text-gray-600">
          Example of creating a completely custom form for specific use cases (cards).
        </p>
        <GenericCssPropertiesForm
          title="Card CSS Properties"
          description="Specialized properties for card components"
          entityId="card-1"
          entityType="card"
          propertyConfigs={CARD_CSS_PROPERTIES}
          onSave={mockSave}
          onLoad={mockLoad}
        />
      </div>

      {/* Usage Instructions */}
      <div className="mt-12 p-6 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">How to Use the Generic CSS Properties Form</h3>
        
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium">1. Basic Usage (Default Properties)</h4>
            <pre className="bg-gray-800 text-green-400 p-3 rounded mt-2 overflow-x-auto">
{`<GenericCssPropertiesForm
  entityId="my-element"
  entityType="element"
  onSave={handleSave}
  onLoad={handleLoad}
/>`}
            </pre>
          </div>

          <div>
            <h4 className="font-medium">2. Custom Properties</h4>
            <pre className="bg-gray-800 text-green-400 p-3 rounded mt-2 overflow-x-auto">
{`const customProperties = [
  { key: "width", label: "Width", type: "input", category: "Layout", placeholder: "100px" },
  { key: "color", label: "Color", type: "input", category: "Style", placeholder: "#000" }
];

<GenericCssPropertiesForm
  propertyConfigs={customProperties}
  entityId="my-element"
  entityType="element"
  onSave={handleSave}
/>`}
            </pre>
          </div>

          <div>
            <h4 className="font-medium">3. Custom Trigger</h4>
            <pre className="bg-gray-800 text-green-400 p-3 rounded mt-2 overflow-x-auto">
{`<GenericCssPropertiesForm {...props}>
  <Button variant="outline">
    <Settings className="h-4 w-4 mr-2" />
    Customize Styles
  </Button>
</GenericCssPropertiesForm>`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CssFormExamples;
