"use client";

import React from "react";
import { Section } from "@prisma/client";
import { Settings } from "lucide-react";
import { toast } from "sonner";
import {
  updateCssPropertiesAction,
  getCssPropertiesAction,
} from "@/actions/css-properties";
import { GenericCssPropertiesForm } from "@/components/ui/css-properties-form";

type Props = {
  children?: React.ReactNode;
  section: Section;
};

const SectionCssPropertiesForm = ({ children, section }: Props) => {
  const handleSave = async (styles: Record<string, any>) => {
    try {
      const result = await updateCssPropertiesAction({
        sectionId: section.id,
        styles,
      });

      if (result.error) {
        throw new Error(result.message);
      }

      toast.success("Section CSS properties saved successfully!");
      // Refresh the page to see the changes immediately
      window.location.reload();
    } catch (error) {
      console.error("Error saving CSS properties:", error);
      throw error; // Re-throw to let the generic form handle the error
    }
  };

  const handleLoad = async () => {
    try {
      const result = await getCssPropertiesAction(section.id);

      if (result.error) {
        throw new Error(result.message);
      }

      return (result.data?.styles as Record<string, any>) || {};
    } catch (error) {
      console.error("Error loading CSS properties:", error);
      return null;
    }
  };

  return (
    <GenericCssPropertiesForm
      title="Section CSS Properties"
      description="Customize the styling properties for this section"
      entityId={section.id}
      entityType="section"
      onSave={handleSave}
      onLoad={handleLoad}
    >
      {children || (
        <button className="flex items-center gap-1 px-2 py-1 text-xs bg-white/10 hover:bg-white/20 rounded transition-colors">
          <Settings className="h-3 w-3" />
          CSS
        </button>
      )}
    </GenericCssPropertiesForm>
  );
};

export default SectionCssPropertiesForm;
