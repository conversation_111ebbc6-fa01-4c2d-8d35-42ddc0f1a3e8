"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerDes<PERSON>,
  DrawerTrigger,
  DrawerClose,
  DrawerFooter,
} from "@/components/ui/drawer";
import { Image, Section, SectionItem } from "@prisma/client";
import Form from "next/form";
import { updateImageAction } from "@/actions/image";
import { toast } from "sonner";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";

type Props = {
  children?: React.ReactNode;
  sectionId: SectionItem["id"];
  image?: Image;
};

const ImageForm = ({ sectionId, image, children }: Props) => {
  const [src, setSrc] = useState(image?.src || "");
  const [alt, setAlt] = useState(image?.alt || "");
  const [caption, setCaption] = useState(image?.caption || "");
  const [width, setWidth] = useState(image?.width || "");
  const [height, setHeight] = useState(image?.height || "");

  const handleUpdateImage = async () => {
    const { data, message, error } = await updateImageAction({
      id: image?.id as string,
      src,
      alt,
      caption,
      width,
      height,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };

  return (
    <>
      <Drawer direction="right">
        <DrawerTrigger asChild>{children}</DrawerTrigger>
        <DrawerContent className="h-full w-[600px] ml-auto rounded-l-2xl border-l bg-background/95 backdrop-blur-sm">
          {/* Close button */}
          <div className="absolute top-4 right-4 z-10">
            <DrawerClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full hover:bg-muted/50"
              >
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>

          <DrawerHeader className="pt-8 pb-6 border-b">
            <DrawerTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              🖼️ Update Image
            </DrawerTitle>
            <DrawerDescription className="text-base text-muted-foreground">
              Update the image URL, alt text, caption, width, and height for
              optimal display.
            </DrawerDescription>
          </DrawerHeader>

          <div className="flex-1 overflow-y-auto p-6">
            <Form className="space-y-6" action={handleUpdateImage}>
              <div className="grid grid-cols-1 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="src">Image Source URL</Label>
                  <Textarea
                    id="src"
                    placeholder="Image Source URL"
                    className="peer text-xs"
                    value={src}
                    onChange={(e) => setSrc(e.target.value)}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="alt">Image Alt Text</Label>
                    <Textarea
                      id="alt"
                      placeholder="Describe the image for accessibility"
                      className="peer text-sm min-h-[80px]"
                      value={alt}
                      onChange={(e) => setAlt(e.target.value)}
                    />
                    <p className="text-muted-foreground text-xs">
                      Alt text for screen readers and SEO
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="caption">Image Caption</Label>
                    <Textarea
                      id="caption"
                      placeholder="Optional caption text"
                      className="peer text-sm min-h-[80px]"
                      value={caption}
                      onChange={(e) => setCaption(e.target.value)}
                    />
                    <p className="text-muted-foreground text-xs">
                      Visible caption below the image
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="width">Image Width</Label>
                  <Input
                    type="number"
                    id="width"
                    placeholder="Auto"
                    className="peer text-sm"
                    value={width}
                    onChange={(e) => setWidth(e.target.value)}
                  />
                  <p className="text-muted-foreground text-xs">
                    Width in pixels (optional)
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="height">Image Height</Label>
                  <Input
                    type="number"
                    id="height"
                    placeholder="Auto"
                    className="peer text-sm"
                    value={height}
                    onChange={(e) => setHeight(e.target.value)}
                  />
                  <p className="text-muted-foreground text-xs">
                    Height in pixels (optional)
                  </p>
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Button type="submit" className="flex-1">
                  Update Image
                </Button>
                <DrawerClose asChild>
                  <Button variant="outline" type="button" className="flex-1">
                    Cancel
                  </Button>
                </DrawerClose>
              </div>
            </Form>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default ImageForm;
