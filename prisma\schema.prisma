// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum SectionType {
  BLANK
  VIDEO
  IMAGE
  TEXT
  TEXTIMAGE
  FORM
  SOCIAL
  MAP
  ECOMMERCE
  GALLERY
}

enum SectionElement {
  BUTTON
  IMAGE
  TEXT
  VIDEO
  FORM
  LINK
  ICON
  MAP
  PRODUCT
  GRID
  GRID_ITEM
  GRID_TYPE
  GRID_ITEM_TYPE
  DIV
  PARAGRAPH
  HEADER
  FOOTER
  SECTION
  HEADING
  SUBHEADING
}

enum ExternalLinkType {
  SECTION
  TEXT
}

enum RowPosition {
  RIGHT
  CENTER
  LEFT
}

enum GalleryType {
  GRID
  SLIDER
  CAROUSEL
}

enum EcommerceType {
  PRODUCT
  COLLECTION
}

model User {
  id        String    @id @default(uuid())
  name      String
  email     String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  Project   Project[]

  @@map("users")
}

model Project {
  id        String    @id @default(uuid())
  title     String
  userId    String
  user      User      @relation(fields: [userId], references: [id])
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  sections  Section[]

  @@index([userId, createdAt(sort: Desc)])
  @@map("projects")
}

model Section {
  id            String        @id @default(uuid())
  title         String?
  projectId     String
  index         Int
  project       Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)
  type          SectionType
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  cssProperties CssProperty?
  items         SectionItem[]

  @@index([projectId, index(sort: Asc)])
  @@map("sections")
}

model SectionItem {
  id             String        @id @default(uuid())
  section        Section       @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  sectionId      String        @map("sectionId")
  is_reverse     Boolean       @default(false)
  text           Text?
  externalLink   ExternalLink?
  image          Image?
  video          Video?
  textImage      TextImage?
  social         Social[]
  map            Map?
  gallery        Gallery?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  externalLinkId String?
  imageId        String?       @unique
  videoId        String?       @unique
  textImageId    String?       @unique
  mapId          String?       @unique
  galleryId      String?       @unique
  ecommerce      Ecommerce?
  form           Form?

  @@index([sectionId])
  @@map("section_items")
}

model Text {
  id            String        @id @default(uuid())
  content       String        @default("<h1> This is you heading </h1> <p> you can write as much as you want here. you can write a long paragraph or you can write a short paragraph. click here to check out. </p>")
  rowPosition   RowPosition   @default(CENTER)
  sectionItem   SectionItem?  @relation(fields: [sectionItemId], references: [id], onDelete: Cascade)
  sectionItemId String?       @unique @map("sectionItemId")
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  externalLink  ExternalLink?
  textImage     TextImage?

  @@index([sectionItemId])
  @@map("texts")
}

model ExternalLink {
  id            String            @id @default(uuid())
  label         String?           @default("Click Me!")
  url           String            @default("")
  type          ExternalLinkType? @default(TEXT)
  rowPosition   RowPosition?      @default(CENTER)
  sectionItem   SectionItem?      @relation(fields: [sectionItemId], references: [id], onDelete: Cascade)
  sectionItemId String?           @unique @map("sectionItemId")
  text          Text?             @relation(fields: [textId], references: [id], onDelete: Cascade)
  textId        String?           @unique @map("textId")
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  @@map("external_links")
}

model Image {
  id            String       @id @default(uuid())
  src           String       @default("https://images.unsplash.com/photo-1702834000621-76c4a9d15868?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D")
  alt           String       @default("Placeholder Image")
  caption       String?
  width         String?
  height        String?
  textImage     TextImage?
  Gallery       Gallery?     @relation(fields: [galleryId], references: [id])
  galleryId     String?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  sectionItem   SectionItem? @relation(fields: [sectionItemId], references: [id], onDelete: Cascade)
  sectionItemId String?      @unique @map("sectionItemId")

  @@index([sectionItemId])
  @@map("images")
}

model Video {
  id            String       @id @default(uuid())
  src           String       @default("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
  alt           String       @default("Placeholder Video")
  caption       String?
  width         String?
  height        String?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  sectionItemId String?      @unique @map("sectionItemId")
  sectionItem   SectionItem? @relation(fields: [sectionItemId], references: [id], onDelete: Cascade)

  @@index([sectionItemId])
  @@map("videos")
}

model TextImage {
  id            String       @id @default(uuid())
  textId        String       @unique @map("textId")
  imageId       String       @unique @map("imageId")
  text          Text         @relation(fields: [textId], references: [id], onDelete: Cascade)
  image         Image        @relation(fields: [imageId], references: [id], onDelete: Cascade)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  sectionItemId String?      @unique @map("sectionItemId")
  sectionItem   SectionItem? @relation(fields: [sectionItemId], references: [id], onDelete: Cascade)

  @@index([textId])
  @@index([imageId])
  @@index([sectionItemId])
  @@map("text_images")
}

model Social {
  id            String       @id @default(uuid())
  platform      String       @default("twitter")
  href          String       @default("https://twitter.com/viber")
  icon          String       @default("https://www.svgrepo.com/show/475437/twitter.svg")
  label         String       @default("Twitter")
  color         String       @default("blue")
  size          String       @default("24")
  position      String       @default("left")
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  sectionItem   SectionItem? @relation(fields: [sectionItemId], references: [id])
  sectionItemId String?

  @@index([sectionItemId])
  @@map("socials")
}

model Map {
  id              String       @id @default(uuid())
  latitude        Float?       @default(0.0)
  longitude       Float?       @default(0.0)
  zoom            Int?         @default(10)
  address         String?
  city            String?
  state           String?
  country         String?
  pinCode         String?
  pin             Boolean?     @default(true)
  pinColor        String?      @default("red")
  pinSize         String?      @default("24")
  pinLabel        String?
  pinIcon         String?      @default("https://www.svgrepo.com/show/475437/twitter.svg")
  iframe          Boolean?     @default(true)
  height          String?      @default("400")
  width           String?      @default("100%")
  style           String?
  iframeSrc       String?
  googleApiKey    String?
  googleMapId     String?
  googleMapUrl    String?
  googleMapStyle  String?
  googleMapType   String?      @default("roadmap")
  googleMapZoom   Int?         @default(10)
  googleMapCenter String?      @default("0,0")
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  sectionItem     SectionItem? @relation(fields: [sectionItemId], references: [id], onDelete: Cascade)
  sectionItemId   String?      @unique @map("sectionItemId")

  @@index([sectionItemId])
  @@map("maps")
}

model Gallery {
  id            String       @id @default(uuid())
  sectionItem   SectionItem? @relation(fields: [sectionItemId], references: [id], onDelete: Cascade)
  sectionItemId String?      @unique @map("sectionItemId")
  type          GalleryType  @default(GRID)
  images        Image[]
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  @@index([sectionItemId])
  @@map("galleries")
}

model Form {
  id            String       @id @default(uuid())
  sectionItem   SectionItem? @relation(fields: [sectionItemId], references: [id], onDelete: Cascade)
  sectionItemId String?      @unique @map("sectionItemId")
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  @@index([sectionItemId])
  @@map("forms")
}

model Ecommerce {
  id            String       @id @default(uuid())
  createdAt     DateTime     @default(now())
  sectionItem   SectionItem? @relation(fields: [sectionItemId], references: [id], onDelete: Cascade)
  sectionItemId String?      @unique @map("sectionItemId")
  updatedAt     DateTime     @updatedAt
  products      Product[]

  @@index([sectionItemId])
  @@map("ecommerces")
}

model Product {
  id          String    @id @default(uuid())
  name        String?
  price       Float?
  image       String?
  description String?
  rating      Float?    @default(0.0)
  stock       Int?      @default(0)
  category    String?
  tags        String?
  isFeatured  Boolean   @default(false)
  link        String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  ecommerce   Ecommerce @relation(fields: [ecommerceId], references: [id], onDelete: Cascade)
  ecommerceId String    @unique @map("ecommerceId")

  @@index([ecommerceId])
  @@map("products")
}

model CssProperty {
  id        String   @id @default(uuid())
  styles    Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Section   Section? @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  sectionId String?  @unique @map("sectionId")

  @@index([sectionId])
  @@map("css_properties")
}

model Properties {
  id            String  @id @default(uuid())
  layoutId      String? @unique
  spacingId     String? @unique
  borderId      String? @unique
  backgroundId  String? @unique
  typographyId  String? @unique
  flexboxId     String? @unique
  gridId        String? @unique
  visualId      String? @unique
  interactionId String? @unique
  objectId      String? @unique
  animationId   String? @unique
  extraId       String? @unique

  layout      LayoutCss?      @relation(fields: [layoutId], references: [id])
  spacing     SpacingCss?     @relation(fields: [spacingId], references: [id])
  border      BorderCss?      @relation(fields: [borderId], references: [id])
  background  BackgroundCss?  @relation(fields: [backgroundId], references: [id])
  typography  TypographyCss?  @relation(fields: [typographyId], references: [id])
  flexbox     FlexboxCss?     @relation(fields: [flexboxId], references: [id])
  grid        GridCss?        @relation(fields: [gridId], references: [id])
  visual      VisualCss?      @relation(fields: [visualId], references: [id])
  interaction InteractionCss? @relation(fields: [interactionId], references: [id])
  object      ObjectCss?      @relation(fields: [objectId], references: [id])
  animation   AnimationCss?   @relation(fields: [animationId], references: [id])
  extra       ExtraCss?       @relation(fields: [extraId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("properties")
}

model LayoutCss {
  id         String      @id @default(uuid())
  display    String?
  position   String?
  top        String?
  right      String?
  bottom     String?
  left       String?
  zIndex     String?
  visibility String?
  boxSizing  String?
  isolation  String?
  properties Properties?
}

model SpacingCss {
  id         String      @id @default(uuid())
  margin     String?
  padding    String?
  gap        String?
  rowGap     String?
  columnGap  String?
  properties Properties?
}

model BorderCss {
  id           String      @id @default(uuid())
  border       String?
  borderRadius String?
  borderColor  String?
  borderWidth  String?
  borderStyle  String?
  properties   Properties?
}

model BackgroundCss {
  id                   String      @id @default(uuid())
  backgroundColor      String?
  backgroundImage      String?
  backgroundSize       String?
  backgroundRepeat     String?
  backgroundPosition   String?
  backgroundAttachment String?
  properties           Properties?
}

model TypographyCss {
  id             String      @id @default(uuid())
  fontFamily     String?
  fontSize       String?
  fontWeight     String?
  lineHeight     String?
  letterSpacing  String?
  textAlign      String?
  textTransform  String?
  textDecoration String?
  textShadow     String?
  whiteSpace     String?
  wordBreak      String?
  color          String?
  properties     Properties?
}

model FlexboxCss {
  id             String      @id @default(uuid())
  flexDirection  String?
  flexWrap       String?
  justifyContent String?
  alignItems     String?
  alignContent   String?
  flex           String?
  flexGrow       String?
  flexShrink     String?
  flexBasis      String?
  order          String?
  flexFlow       String?
  properties     Properties?
}

model GridCss {
  id                  String      @id @default(uuid())
  gridTemplateColumns String?
  gridTemplateRows    String?
  gridTemplateAreas   String?
  gridAutoFlow        String?
  gridAutoColumns     String?
  gridAutoRows        String?
  gridColumnGap       String?
  gridRowGap          String?
  gridGap             String?
  gridColumn          String?
  gridRow             String?
  gridArea            String?
  grid                String?
  justifySelf         String?
  alignSelf           String?
  placeSelf           String?
  justifyItems        String?
  placeContent        String?
  Properties          Properties?
}

model VisualCss {
  id           String      @id @default(uuid())
  boxShadow    String?
  opacity      String?
  filter       String?
  transform    String?
  transition   String?
  mixBlendMode String?
  clipPath     String?
  willChange   String?
  Properties   Properties?
}

model InteractionCss {
  id            String      @id @default(uuid())
  cursor        String?
  userSelect    String?
  pointerEvents String?
  Properties    Properties?
}

model ObjectCss {
  id             String      @id @default(uuid())
  objectFit      String?
  objectPosition String?
  Properties     Properties?
}

model AnimationCss {
  id                      String      @id @default(uuid())
  animation               String?
  animationName           String?
  animationDuration       String?
  animationTimingFunction String?
  animationDelay          String?
  animationIterationCount String?
  animationDirection      String?
  animationFillMode       String?
  animationPlayState      String?
  Properties              Properties?
}

model ExtraCss {
  id                String      @id @default(uuid())
  aspectRatio       String?
  direction         String?
  writingMode       String?
  outline           String?
  outlineColor      String?
  outlineWidth      String?
  outlineOffset     String?
  scrollBehavior    String?
  borderCollapse    String?
  borderSpacing     String?
  tableLayout       String?
  captionSide       String?
  listStyle         String?
  listStyleType     String?
  listStylePosition String?
  contentVisibility String?
  contain           String?
  columnCount       String?
  columnGap         String?
  columnWidth       String?
  columnRule        String?
  Properties        Properties?
}
