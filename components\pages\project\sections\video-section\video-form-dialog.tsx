"use client";

import React from "react";
import { Section, Video } from "@prisma/client";
import {
  Drawer,
  DrawerClose,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Drawer<PERSON>eader,
  Drawer<PERSON>rigger,
  DrawerTitle,
  DrawerDescription,
} from "@/components/ui/drawer";
import { But<PERSON> } from "@/components/ui/button";
import VideoForm from "./video-form";
import { X } from "lucide-react";

type Props = {
  children?: React.ReactNode;
  sectionId: Section["id"];
  video?: Video;
};

const VideoFormDialog = ({ children, sectionId, video }: Props) => {
  return (
    <>
      <Drawer direction="right">
        <DrawerTrigger asChild>{children}</DrawerTrigger>
        <DrawerContent className="h-full w-[500px] ml-auto rounded-l-2xl border-l bg-background/95 backdrop-blur-sm">
          {/* Close button */}
          <div className="absolute top-4 right-4 z-10">
            <DrawerClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full hover:bg-muted/50"
              >
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>

          <DrawerHeader className="space-y-3 pt-8 pb-6 border-b">
            <DrawerTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              🎥 Update Video
            </DrawerTitle>
            <DrawerDescription className="text-base text-muted-foreground">
              Customize your video settings including URL, title, caption, and
              dimensions for the perfect display.
            </DrawerDescription>
          </DrawerHeader>

          <div className="flex-1 overflow-y-auto p-6">
            <VideoForm sectionId={sectionId} video={video} />
          </div>

          <DrawerFooter className="gap-2 border-t bg-muted/20">
            <DrawerClose asChild>
              <Button
                variant="outline"
                type="button"
                className="hover:bg-muted/50"
              >
                Cancel
              </Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default VideoFormDialog;
