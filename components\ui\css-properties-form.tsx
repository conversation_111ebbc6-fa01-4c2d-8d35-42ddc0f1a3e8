"use client";

import React, { useState, useEffect } from "react";
import {
  Drawer,
  Drawer<PERSON>ontent,
  Drawer<PERSON>eader,
  DrawerTitle,
  DrawerDescription,
  DrawerTrigger,
  DrawerClose,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { X, Settings, Palette, Code2 } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CssPropertyConfig, GenericCssPropertiesFormProps } from "@/lib/types";
import { DEFAULT_CSS_PROPERTIES } from "@/lib/css-form-static";

export const GenericCssPropertiesForm: React.FC<
  GenericCssPropertiesFormProps
> = ({
  children,
  title = "CSS Properties",
  description = "Customize the styling properties",
  entityId,
  entityType,
  initialStyles = {},
  onSave,
  onLoad,
  propertyConfigs = DEFAULT_CSS_PROPERTIES,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [jsonInput, setJsonInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Initialize quick styles from property configs
  const initializeQuickStyles = () => {
    const styles: Record<string, string> = {};
    propertyConfigs.forEach((config) => {
      styles[config.key] = initialStyles[config.key] || "";
    });
    return styles;
  };

  const [quickStyles, setQuickStyles] = useState(initializeQuickStyles);

  // Load existing styles
  useEffect(() => {
    if (isOpen && onLoad) {
      loadExistingStyles();
    }
  }, [isOpen]);

  const loadExistingStyles = async () => {
    try {
      const styles = await onLoad?.();
      if (styles) {
        updateStylesFromJson(styles);
      }
    } catch (error) {
      console.error("Error loading styles:", error);
      toast.error("Failed to load existing styles");
    }
  };

  const updateStylesFromJson = (styles: Record<string, any>) => {
    const newQuickStyles = initializeQuickStyles();
    Object.keys(styles).forEach((key) => {
      if (newQuickStyles.hasOwnProperty(key)) {
        newQuickStyles[key] = styles[key] || "";
      }
    });
    setQuickStyles(newQuickStyles);
    setJsonInput(JSON.stringify(styles, null, 2));
  };

  const handleQuickStyleChange = (property: string, value: string) => {
    const processedValue = value === "default" ? "" : value;

    setQuickStyles((prev) => ({
      ...prev,
      [property]: processedValue,
    }));

    // Update JSON input with quick styles
    try {
      const currentStyles = JSON.parse(jsonInput || "{}");
      const updatedStyles = {
        ...currentStyles,
        [property]: processedValue || undefined,
      };

      // Remove undefined values
      Object.keys(updatedStyles).forEach((key) => {
        if (updatedStyles[key] === undefined || updatedStyles[key] === "") {
          delete updatedStyles[key];
        }
      });

      setJsonInput(JSON.stringify(updatedStyles, null, 2));
    } catch (error) {
      console.error("Error updating JSON:", error);
    }
  };

  const handleJsonChange = (value: string) => {
    setJsonInput(value);

    try {
      const parsed = JSON.parse(value || "{}");
      updateStylesFromJson(parsed);
    } catch (error) {
      // Invalid JSON, don't update quick styles
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const styles = JSON.parse(jsonInput || "{}");
      await onSave(styles);
      toast.success("CSS properties saved successfully!");
      setIsOpen(false);
    } catch (error) {
      console.error("Error saving CSS properties:", error);
      toast.error("Failed to save CSS properties");
    } finally {
      setIsLoading(false);
    }
  };

  // Group properties by category
  const groupedProperties = propertyConfigs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, CssPropertyConfig[]>);

  const renderPropertyField = (config: CssPropertyConfig) => {
    const value = quickStyles[config.key] || "";

    if (config.type === "select" && config.options) {
      return (
        <Select
          value={value}
          onValueChange={(newValue) =>
            handleQuickStyleChange(config.key, newValue)
          }
        >
          <SelectTrigger>
            <SelectValue placeholder={`Select ${config.label.toLowerCase()}`} />
          </SelectTrigger>
          <SelectContent>
            {config.options.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    if (config.type === "textarea") {
      return (
        <Textarea
          placeholder={config.placeholder}
          value={value}
          onChange={(e) => handleQuickStyleChange(config.key, e.target.value)}
        />
      );
    }

    return (
      <Input
        placeholder={config.placeholder}
        value={value}
        onChange={(e) => handleQuickStyleChange(config.key, e.target.value)}
      />
    );
  };

  return (
    <Drawer direction="right" open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild>
        {children || (
          <Button
            variant="outline"
            size="sm"
            className={cn("gap-2", className)}
          >
            <Settings className="h-4 w-4" />
            CSS Properties
          </Button>
        )}
      </DrawerTrigger>
      <DrawerContent className="min-w-5xl">
        <DrawerHeader className="border-b">
          <div className="flex items-center justify-between">
            <div>
              <DrawerTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                {title}
              </DrawerTitle>
              <DrawerDescription>{description}</DrawerDescription>
            </div>
            <DrawerClose asChild>
              <Button variant="ghost" size="sm">
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        <div className="flex-1 overflow-auto p-6">
          <Tabs defaultValue="quick" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="quick" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Quick Editor
              </TabsTrigger>
              <TabsTrigger value="json" className="flex items-center gap-2">
                <Code2 className="h-4 w-4" />
                JSON Editor
              </TabsTrigger>
            </TabsList>

            <TabsContent value="quick" className="space-y-6 mt-6">
              {Object.entries(groupedProperties).map(
                ([category, properties]) => (
                  <div key={category} className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                      {category}
                    </h3>
                    <div className="grid grid-cols-1 gap-4">
                      {properties.map((config) => (
                        <div key={config.key} className="space-y-2">
                          <Label htmlFor={config.key}>{config.label}</Label>
                          {renderPropertyField(config)}
                        </div>
                      ))}
                    </div>
                  </div>
                )
              )}
            </TabsContent>

            <TabsContent value="json" className="space-y-4 mt-6">
              <div className="space-y-2">
                <Label htmlFor="jsonEditor">CSS Properties (JSON Format)</Label>
                <Textarea
                  id="jsonEditor"
                  placeholder='{"color": "red", "fontSize": "16px"}'
                  value={jsonInput}
                  onChange={(e) => handleJsonChange(e.target.value)}
                  className="min-h-[300px] font-mono text-sm"
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="border-t p-6">
          <div className="flex gap-3 justify-end">
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default GenericCssPropertiesForm;
