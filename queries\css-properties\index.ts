import db from "@/lib/db";
import { CssPropertiesData } from "@/actions/css-properties";

export const updateCssPropertiesQuery = async (data: CssPropertiesData) => {
  const { sectionId, styles } = data;

  try {
    // First, try to find existing CSS properties for this section
    const existingCssProperties = await db.cssProperty.findUnique({
      where: {
        sectionId: sectionId,
      },
    });

    if (existingCssProperties) {
      // Update existing CSS properties
      return await db.cssProperty.update({
        where: {
          sectionId: sectionId,
        },
        data: {
          styles: styles,
        },
      });
    } else {
      // Create new CSS properties
      return await db.cssProperty.create({
        data: {
          sectionId: sectionId,
          styles: styles,
        },
      });
    }
  } catch (error) {
    console.error("Error in updateCssPropertiesQuery:", error);
    throw error;
  }
};

export const getCssPropertiesQuery = async (sectionId: string) => {
  try {
    return await db.cssProperty.findUnique({
      where: {
        sectionId: sectionId,
      },
    });
  } catch (error) {
    console.error("Error in getCssPropertiesQuery:", error);
    throw error;
  }
};

export const deleteCssPropertiesQuery = async (sectionId: string) => {
  try {
    return await db.cssProperty.delete({
      where: {
        sectionId: sectionId,
      },
    });
  } catch (error) {
    console.error("Error in deleteCssPropertiesQuery:", error);
    throw error;
  }
};
