"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LetterText } from "lucide-react";
import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import SectionOptions from "./section-options";
import { createSectionAction } from "@/actions/section";
import { $Enums, Section, Video } from "@prisma/client";
import { toast } from "sonner";
import VideoForm from "../video-section/video-form";

type Props = {
  children?: React.ReactNode;
  index: Section["index"];
  projectId: Section["projectId"];
  sectionId?: Section["id"];
};

const SectionOptionDialog = ({
  children,
  index,
  projectId,
  sectionId,
}: Props) => {
  const createSectionWithType = async (
    type: $Enums.SectionType,
    src?: Video["src"]
  ) => {
    const { data, message, error } = await createSectionAction({
      projectId: projectId,
      index: index,
      type,
      src: src,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };

  const [SelectedType, setSelectedType] = React.useState<$Enums.SectionType>();

  const onSelete = async (type: $Enums.SectionType) => {
    if (type === $Enums.SectionType.VIDEO) {
      setSelectedType(type);
    } else {
      await createSectionWithType(type);
    }
  };
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="min-w-7xl max-h-screen overflow-y-auto">
        {SelectedType === undefined && (
          <DialogHeader className="text-center pb-4">
            <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
              <LetterText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Choose Your Section Type
            </DialogTitle>
            <p className="text-muted-foreground mt-2">
              Select the type of content you want to add to your website
            </p>
          </DialogHeader>
        )}
        {SelectedType === $Enums.SectionType.VIDEO && (
          <DialogHeader>
            <DialogTitle>Video Section</DialogTitle>
            <p className="text-muted-foreground mt-2">
              Add a video to your website enter URL
            </p>
          </DialogHeader>
        )}
        {SelectedType === $Enums.SectionType.VIDEO && (
          <VideoForm
            sectionId={sectionId}
            onSubmit={createSectionWithType.bind(null, SelectedType)}
            p={"create"}
          />
        )}
        {SelectedType !== $Enums.SectionType.VIDEO && (
          <SectionOptions onSelect={onSelete} />
        )}
        <DialogFooter className="pt-4">
          <DialogClose asChild>
            <Button variant="outline" className="w-full sm:w-auto">
              Cancel
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SectionOptionDialog;
