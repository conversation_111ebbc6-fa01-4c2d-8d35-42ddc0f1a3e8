"use client";

import React from "react";
import { Section, Social } from "@prisma/client";
import { But<PERSON> } from "@/components/ui/button";
import { Edit2Icon, ExternalLink } from "lucide-react";
import { cn } from "@/lib/utils";

type Props = {
  section: Section & {
    social?: Social[] | null;
  };
};

const SocialSection = ({ section }: Props) => {
  if (!section.social || section.social.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No social links configured</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Modern Social Links Grid */}
      <div className="flex flex-wrap justify-center gap-4">
        {section.social.map((social) => (
          <div
            key={social.id}
            className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-background via-background to-muted/20 p-1 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
          >
            {/* Gradient border effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl opacity-0 group-hover:opacity-75 transition-opacity duration-500 blur-sm"></div>
            
            {/* Social link container */}
            <div className="relative bg-background rounded-xl p-4 flex flex-col items-center space-y-3 min-w-[120px]">
              {/* Social icon */}
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-muted to-muted/50 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                {social.icon ? (
                  <img 
                    src={social.icon} 
                    alt={social.platform} 
                    className="w-6 h-6"
                  />
                ) : (
                  <ExternalLink className="w-6 h-6 text-muted-foreground" />
                )}
              </div>
              
              {/* Social platform name */}
              <h3 className="text-sm font-semibold text-foreground text-center">
                {social.label || social.platform}
              </h3>
              
              {/* Link button */}
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs hover:bg-primary hover:text-primary-foreground transition-colors duration-300"
                onClick={() => window.open(social.href, '_blank')}
              >
                Visit
              </Button>
              
              {/* Edit overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <Button
                  variant="secondary"
                  size="sm"
                  className="backdrop-blur-md bg-white/10 border-white/20 text-foreground hover:bg-white/20 shadow-lg"
                >
                  <Edit2Icon className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SocialSection;
