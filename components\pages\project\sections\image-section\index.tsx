"use client";

import React from "react";
import Image from "next/image";
import { Section, Image as Images, SectionItem } from "@prisma/client";
import { Button } from "@/components/ui/button";
import ImageForm from "./image-form";
import { cn } from "@/lib/utils";
import { Edit2Icon } from "lucide-react";

type Props = {
  section: Section & {
    items: (SectionItem & { image: Images | null })[];
  };
};

const ImageSection = ({ section }: Props) => {
  // Get the first item from the items array
  const firstItem = section.items?.[0];
  if (!firstItem?.image) {
    return null;
  }

  return (
    <figure className="relative flex flex-col items-center group/image-section max-w-4xl mx-auto">
      {/* Image container with modern styling */}
      <div className="relative overflow-hidden rounded-2xl shadow-2xl bg-gradient-to-br from-slate-100 via-slate-50 to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-2">
        {/* Gradient border effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl opacity-0 group-hover/image-section:opacity-75 transition-opacity duration-500 blur-sm"></div>

        {/* Image content */}
        <div className="relative bg-background rounded-xl overflow-hidden">
          {/* Edit button overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover/image-section:bg-black/20 transition-all duration-300 flex items-center justify-center z-10">
            <ImageForm sectionId={firstItem.id} image={firstItem.image}>
              <Button
                variant="secondary"
                size="sm"
                className="opacity-0 group-hover/image-section:opacity-100 transition-all duration-300 transform scale-95 group-hover/image-section:scale-100 backdrop-blur-md bg-white/10 border-white/20 text-white hover:bg-white/20 shadow-lg gap-2"
              >
                <Edit2Icon className="w-4 h-4" />
                Edit Image
              </Button>
            </ImageForm>
          </div>

          {/* Image element */}
          <Image
            src={firstItem.image?.src as string}
            alt={firstItem.image?.alt as string}
            width={firstItem.image?.width ? Number(firstItem.image.width) : 800}
            height={
              firstItem.image?.height ? Number(firstItem.image.height) : 600
            }
            className={cn(
              "w-full h-auto object-cover transition-transform duration-500 group-hover/image-section:scale-105",
              firstItem.image?.width && firstItem.image?.height
                ? "aspect-auto"
                : "aspect-video"
            )}
          />
        </div>
      </div>

      {/* Enhanced caption and alt text */}
      <div className="mt-6 text-center space-y-2 max-w-2xl">
        {firstItem.image?.caption && (
          <figcaption className="text-muted-foreground leading-relaxed">
            {firstItem.image.caption}
          </figcaption>
        )}
      </div>
    </figure>
  );
};

export default ImageSection;
