/*
  Warnings:

  - You are about to drop the column `sectionId` on the `external_links` table. All the data in the column will be lost.
  - You are about to drop the column `sectionId` on the `galleries` table. All the data in the column will be lost.
  - You are about to drop the column `sectionId` on the `images` table. All the data in the column will be lost.
  - You are about to drop the column `sectionId` on the `maps` table. All the data in the column will be lost.
  - You are about to drop the column `is_reverse` on the `sections` table. All the data in the column will be lost.
  - You are about to drop the column `sectionId` on the `socials` table. All the data in the column will be lost.
  - You are about to drop the column `sectionId` on the `text_images` table. All the data in the column will be lost.
  - You are about to drop the column `sectionId` on the `texts` table. All the data in the column will be lost.
  - You are about to drop the column `sectionId` on the `videos` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[section_item_id]` on the table `external_links` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[section_item_id]` on the table `galleries` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[section_item_id]` on the table `images` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[section_item_id]` on the table `maps` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[section_item_id]` on the table `text_images` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[section_item_id]` on the table `texts` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[section_item_id]` on the table `videos` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "external_links" DROP CONSTRAINT "external_links_sectionId_fkey";

-- DropForeignKey
ALTER TABLE "galleries" DROP CONSTRAINT "galleries_sectionId_fkey";

-- DropForeignKey
ALTER TABLE "images" DROP CONSTRAINT "images_sectionId_fkey";

-- DropForeignKey
ALTER TABLE "maps" DROP CONSTRAINT "maps_sectionId_fkey";

-- DropForeignKey
ALTER TABLE "socials" DROP CONSTRAINT "socials_sectionId_fkey";

-- DropForeignKey
ALTER TABLE "text_images" DROP CONSTRAINT "text_images_sectionId_fkey";

-- DropForeignKey
ALTER TABLE "texts" DROP CONSTRAINT "texts_sectionId_fkey";

-- DropForeignKey
ALTER TABLE "videos" DROP CONSTRAINT "videos_sectionId_fkey";

-- DropIndex
DROP INDEX "external_links_sectionId_key";

-- DropIndex
DROP INDEX "galleries_sectionId_idx";

-- DropIndex
DROP INDEX "galleries_sectionId_key";

-- DropIndex
DROP INDEX "images_sectionId_idx";

-- DropIndex
DROP INDEX "images_sectionId_key";

-- DropIndex
DROP INDEX "maps_sectionId_idx";

-- DropIndex
DROP INDEX "maps_sectionId_key";

-- DropIndex
DROP INDEX "socials_sectionId_idx";

-- DropIndex
DROP INDEX "socials_sectionId_key";

-- DropIndex
DROP INDEX "text_images_sectionId_idx";

-- DropIndex
DROP INDEX "text_images_sectionId_key";

-- DropIndex
DROP INDEX "texts_sectionId_idx";

-- DropIndex
DROP INDEX "texts_sectionId_key";

-- DropIndex
DROP INDEX "videos_sectionId_idx";

-- DropIndex
DROP INDEX "videos_sectionId_key";

-- AlterTable
ALTER TABLE "external_links" DROP COLUMN "sectionId",
ADD COLUMN     "section_item_id" TEXT;

-- AlterTable
ALTER TABLE "galleries" DROP COLUMN "sectionId",
ADD COLUMN     "section_item_id" TEXT;

-- AlterTable
ALTER TABLE "images" DROP COLUMN "sectionId",
ADD COLUMN     "section_item_id" TEXT;

-- AlterTable
ALTER TABLE "maps" DROP COLUMN "sectionId",
ADD COLUMN     "section_item_id" TEXT;

-- AlterTable
ALTER TABLE "sections" DROP COLUMN "is_reverse";

-- AlterTable
ALTER TABLE "socials" DROP COLUMN "sectionId",
ADD COLUMN     "sectionItemId" TEXT;

-- AlterTable
ALTER TABLE "text_images" DROP COLUMN "sectionId",
ADD COLUMN     "section_item_id" TEXT;

-- AlterTable
ALTER TABLE "texts" DROP COLUMN "sectionId",
ADD COLUMN     "section_item_id" TEXT;

-- AlterTable
ALTER TABLE "videos" DROP COLUMN "sectionId",
ADD COLUMN     "section_item_id" TEXT;

-- CreateTable
CREATE TABLE "section_items" (
    "id" TEXT NOT NULL,
    "sectionId" TEXT NOT NULL,
    "is_reverse" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "externalLinkId" TEXT,
    "imageId" TEXT,
    "videoId" TEXT,
    "textImageId" TEXT,
    "mapId" TEXT,
    "galleryId" TEXT,

    CONSTRAINT "section_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forms" (
    "id" TEXT NOT NULL,
    "section_item_id" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "forms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ecommerces" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "section_item_id" TEXT,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ecommerces_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "products" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "price" DOUBLE PRECISION,
    "image" TEXT,
    "description" TEXT,
    "rating" DOUBLE PRECISION DEFAULT 0.0,
    "stock" INTEGER DEFAULT 0,
    "category" TEXT,
    "tags" TEXT,
    "isFeatured" BOOLEAN NOT NULL DEFAULT false,
    "link" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "ecommerceId" TEXT NOT NULL,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "section_items_sectionId_key" ON "section_items"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "section_items_imageId_key" ON "section_items"("imageId");

-- CreateIndex
CREATE UNIQUE INDEX "section_items_videoId_key" ON "section_items"("videoId");

-- CreateIndex
CREATE UNIQUE INDEX "section_items_textImageId_key" ON "section_items"("textImageId");

-- CreateIndex
CREATE UNIQUE INDEX "section_items_mapId_key" ON "section_items"("mapId");

-- CreateIndex
CREATE UNIQUE INDEX "section_items_galleryId_key" ON "section_items"("galleryId");

-- CreateIndex
CREATE INDEX "section_items_sectionId_idx" ON "section_items"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "forms_section_item_id_key" ON "forms"("section_item_id");

-- CreateIndex
CREATE INDEX "forms_section_item_id_idx" ON "forms"("section_item_id");

-- CreateIndex
CREATE UNIQUE INDEX "ecommerces_section_item_id_key" ON "ecommerces"("section_item_id");

-- CreateIndex
CREATE INDEX "ecommerces_section_item_id_idx" ON "ecommerces"("section_item_id");

-- CreateIndex
CREATE UNIQUE INDEX "products_ecommerceId_key" ON "products"("ecommerceId");

-- CreateIndex
CREATE INDEX "products_ecommerceId_idx" ON "products"("ecommerceId");

-- CreateIndex
CREATE UNIQUE INDEX "external_links_section_item_id_key" ON "external_links"("section_item_id");

-- CreateIndex
CREATE UNIQUE INDEX "galleries_section_item_id_key" ON "galleries"("section_item_id");

-- CreateIndex
CREATE INDEX "galleries_section_item_id_idx" ON "galleries"("section_item_id");

-- CreateIndex
CREATE UNIQUE INDEX "images_section_item_id_key" ON "images"("section_item_id");

-- CreateIndex
CREATE INDEX "images_section_item_id_idx" ON "images"("section_item_id");

-- CreateIndex
CREATE UNIQUE INDEX "maps_section_item_id_key" ON "maps"("section_item_id");

-- CreateIndex
CREATE INDEX "maps_section_item_id_idx" ON "maps"("section_item_id");

-- CreateIndex
CREATE INDEX "socials_sectionItemId_idx" ON "socials"("sectionItemId");

-- CreateIndex
CREATE UNIQUE INDEX "text_images_section_item_id_key" ON "text_images"("section_item_id");

-- CreateIndex
CREATE INDEX "text_images_section_item_id_idx" ON "text_images"("section_item_id");

-- CreateIndex
CREATE UNIQUE INDEX "texts_section_item_id_key" ON "texts"("section_item_id");

-- CreateIndex
CREATE INDEX "texts_section_item_id_idx" ON "texts"("section_item_id");

-- CreateIndex
CREATE UNIQUE INDEX "videos_section_item_id_key" ON "videos"("section_item_id");

-- CreateIndex
CREATE INDEX "videos_section_item_id_idx" ON "videos"("section_item_id");

-- AddForeignKey
ALTER TABLE "section_items" ADD CONSTRAINT "section_items_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "sections"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "texts" ADD CONSTRAINT "texts_section_item_id_fkey" FOREIGN KEY ("section_item_id") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "external_links" ADD CONSTRAINT "external_links_section_item_id_fkey" FOREIGN KEY ("section_item_id") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "images" ADD CONSTRAINT "images_section_item_id_fkey" FOREIGN KEY ("section_item_id") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "videos" ADD CONSTRAINT "videos_section_item_id_fkey" FOREIGN KEY ("section_item_id") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "text_images" ADD CONSTRAINT "text_images_section_item_id_fkey" FOREIGN KEY ("section_item_id") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "socials" ADD CONSTRAINT "socials_sectionItemId_fkey" FOREIGN KEY ("sectionItemId") REFERENCES "section_items"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "maps" ADD CONSTRAINT "maps_section_item_id_fkey" FOREIGN KEY ("section_item_id") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "galleries" ADD CONSTRAINT "galleries_section_item_id_fkey" FOREIGN KEY ("section_item_id") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forms" ADD CONSTRAINT "forms_section_item_id_fkey" FOREIGN KEY ("section_item_id") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ecommerces" ADD CONSTRAINT "ecommerces_section_item_id_fkey" FOREIGN KEY ("section_item_id") REFERENCES "section_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "products" ADD CONSTRAINT "products_ecommerceId_fkey" FOREIGN KEY ("ecommerceId") REFERENCES "ecommerces"("id") ON DELETE CASCADE ON UPDATE CASCADE;
