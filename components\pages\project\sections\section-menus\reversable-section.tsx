"use client";
import React from "react";
import {
  Section,
  Text,
  ExternalLink,
  Image as Images,
  SectionItem,
} from "@prisma/client";
import { Button } from "@/components/ui/button";
import { ArrowRightLeft } from "lucide-react";
import { toast } from "sonner";
import { updateSectionAction } from "@/actions/section";

type Props = {
  section: Section & {
    items: SectionItem & { text: Text | null } & {
      externalLink: ExternalLink | null;
    } & { image: Images | null };
  };
};

const ReversableSection = ({ section }: Props) => {
  const handleReversableSection = async () => {
    const { data, message, error } = await updateSectionAction({
      id: section.items.id,
      is_reverse: !section.items.is_reverse,
    });

    if (error) {
      toast.error(error.message);
    }

    if (data) {
      toast.success(message);
    }
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="External Link section"
        onClick={handleReversableSection}
      >
        <ArrowRightLeft className="h-4 w-4" />
      </Button>
    </>
  );
};

export default ReversableSection;
