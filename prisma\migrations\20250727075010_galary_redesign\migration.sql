/*
  Warnings:

  - You are about to drop the `_GalleryImage` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `gallery_images` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "GalleryType" AS ENUM ('GRID', 'SLIDER', 'CARO<PERSON><PERSON>');

-- <PERSON>reateEnum
CREATE TYPE "EcommerceType" AS ENUM ('PRODUCT', 'COLLECTION');

-- DropForeignKey
ALTER TABLE "_GalleryImage" DROP CONSTRAINT "_GalleryImage_A_fkey";

-- DropForeignKey
ALTER TABLE "_GalleryImage" DROP CONSTRAINT "_GalleryImage_B_fkey";

-- DropForeignKey
ALTER TABLE "gallery_images" DROP CONSTRAINT "gallery_images_galleryId_fkey";

-- DropForeignKey
ALTER TABLE "gallery_images" DROP CONSTRAINT "gallery_images_imageId_fkey";

-- AlterTable
ALTER TABLE "galleries" ADD COLUMN     "type" "GalleryType" NOT NULL DEFAULT 'GRID';

-- DropTable
DROP TABLE "_GalleryImage";

-- DropTable
DROP TABLE "gallery_images";
