"use client";

import React, { useState } from "react";
import Form from "next/form";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Section, Video } from "@prisma/client";
import { updateVideoAction } from "@/actions/video";
import { Button } from "@/components/ui/button";

type Props = {
  sectionId?: Section["id"];
  video?: Video;
  onSubmit?: (src: Video["src"]) => void;
  p?: "create" | "update";
};

const VideoForm = ({ sectionId, video, onSubmit, p }: Props) => {
  const [src, setSrc] = useState(video?.src || "");
  const [alt, setAlt] = useState(video?.alt || "");
  const [caption, setCaption] = useState(video?.caption || "");
  const [width, setWidth] = useState(video?.width || "");
  const [height, setHeight] = useState(video?.height || "");

  const handleUpdateVideo = async () => {
    //trasform src to embed url

    const url = new URL(src);
    const embedUrl = url.searchParams.get("v")
      ? `https://www.youtube.com/embed/${url.searchParams.get("v")}`
      : src;

    if (p == "create") {
      onSubmit?.(embedUrl);
      return;
    }

    const { data, message, error } = await updateVideoAction({
      id: video?.id as string,
      src: embedUrl,
      alt,
      caption,
      width,
      height,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };
  return (
    <div className="space-y-6">
      <Form className="space-y-6" action={handleUpdateVideo}>
        {/* Video URL Section */}
        <div className="space-y-3">
          <Label
            htmlFor="src"
            className="text-sm font-semibold text-foreground flex items-center gap-2"
          >
            🔗 Video Source URL
          </Label>
          <Textarea
            id="src"
            placeholder="https://www.youtube.com/watch?v=... or https://vimeo.com/..."
            className="min-h-[80px] text-sm border-2 border-border/50 focus:border-primary/50 rounded-lg transition-colors"
            value={src || ""}
            onChange={(e) => setSrc(e.target.value)}
          />
          <p className="text-xs text-muted-foreground">
            Paste your YouTube, Vimeo, or direct video URL here
          </p>
        </div>

        {p !== "create" && (
          <>
            {/* Title and Caption Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label
                  htmlFor="alt"
                  className="text-sm font-semibold text-foreground flex items-center gap-2"
                >
                  📝 Video Title
                </Label>
                <Textarea
                  id="alt"
                  placeholder="Enter a descriptive title for your video"
                  className="min-h-[80px] text-sm border-2 border-border/50 focus:border-primary/50 rounded-lg transition-colors"
                  value={alt || ""}
                  onChange={(e) => setAlt(e.target.value)}
                />
              </div>
              <div className="space-y-3">
                <Label
                  htmlFor="caption"
                  className="text-sm font-semibold text-foreground flex items-center gap-2"
                >
                  💬 Video Caption
                </Label>
                <Textarea
                  id="caption"
                  placeholder="Add a caption or description for your video"
                  className="min-h-[80px] text-sm border-2 border-border/50 focus:border-primary/50 rounded-lg transition-colors"
                  value={caption || ""}
                  onChange={(e) => setCaption(e.target.value)}
                />
              </div>
            </div>

            {/* Dimensions Section */}
            <div className="space-y-4">
              <Label className="text-sm font-semibold text-foreground flex items-center gap-2">
                📐 Video Dimensions
              </Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="width"
                    className="text-xs text-muted-foreground"
                  >
                    Width (pixels)
                  </Label>
                  <Input
                    type="number"
                    id="width"
                    placeholder="800"
                    className="text-sm border-2 border-border/50 focus:border-primary/50 rounded-lg transition-colors"
                    value={width || ""}
                    onChange={(e) => setWidth(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="height"
                    className="text-xs text-muted-foreground"
                  >
                    Height (pixels)
                  </Label>
                  <Input
                    type="number"
                    id="height"
                    placeholder="450"
                    className="text-sm border-2 border-border/50 focus:border-primary/50 rounded-lg transition-colors"
                    value={height || ""}
                    onChange={(e) => setHeight(e.target.value)}
                  />
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                Leave empty for responsive sizing (recommended)
              </p>
            </div>
          </>
        )}

        <Button
          type="submit"
          className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
        >
          {p === "create" ? "Add Video" : "Update Video"}
        </Button>
      </Form>
    </div>
  );
};

export default VideoForm;
