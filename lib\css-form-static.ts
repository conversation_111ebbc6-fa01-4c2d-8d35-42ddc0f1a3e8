import { CssPropertyConfig } from "./types";

// Default CSS property configurations
export const DEFAULT_CSS_PROPERTIES: CssPropertyConfig[] = [
  // Layout & Display
  {
    key: "display",
    label: "Display",
    type: "select",
    category: "Layout & Display",
    options: [
      { value: "default", label: "Default" },
      { value: "block", label: "Block" },
      { value: "inline", label: "Inline" },
      { value: "inline-block", label: "Inline Block" },
      { value: "flex", label: "Flex" },
      { value: "grid", label: "Grid" },
      { value: "none", label: "None" },
    ],
  },
  {
    key: "position",
    label: "Position",
    type: "select",
    category: "Layout & Display",
    options: [
      { value: "default", label: "Default" },
      { value: "static", label: "Static" },
      { value: "relative", label: "Relative" },
      { value: "absolute", label: "Absolute" },
      { value: "fixed", label: "Fixed" },
      { value: "sticky", label: "Sticky" },
    ],
  },
  {
    key: "overflow",
    label: "Overflow",
    type: "select",
    category: "Layout & Display",
    options: [
      { value: "default", label: "Default" },
      { value: "visible", label: "Visible" },
      { value: "hidden", label: "Hidden" },
      { value: "scroll", label: "Scroll" },
      { value: "auto", label: "Auto" },
    ],
  },
  {
    key: "justifyContent",
    label: "Justify Content",
    type: "select",
    category: "Layout & Display",
    options: [
      { value: "default", label: "Default" },
      { value: "flex-start", label: "Flex Start" },
      { value: "flex-end", label: "Flex End" },
      { value: "center", label: "Center" },
      { value: "space-between", label: "Space Between" },
      { value: "space-around", label: "Space Around" },
      { value: "space-evenly", label: "Space Evenly" },
    ],
  },
  {
    key: "alignItems",
    label: "Align Items",
    type: "select",
    category: "Layout & Display",
    options: [
      { value: "default", label: "Default" },
      { value: "flex-start", label: "Flex Start" },
      { value: "flex-end", label: "Flex End" },
      { value: "center", label: "Center" },
      { value: "baseline", label: "Baseline" },
      { value: "stretch", label: "Stretch" },
    ],
  },
  {
    key: "flexDirection",
    label: "Flex Direction",
    type: "select",
    category: "Layout & Display",
    options: [
      { value: "default", label: "Default" },
      { value: "row", label: "Row" },
      { value: "row-reverse", label: "Row Reverse" },
      { value: "column", label: "Column" },
      { value: "column-reverse", label: "Column Reverse" },
    ],
  },
  {
    key: "gap",
    label: "Gap",
    type: "input",
    category: "Layout & Display",
    placeholder: "10px, 1rem",
  },
  {
    key: "zIndex",
    label: "Z-Index",
    type: "input",
    category: "Layout & Display",
    placeholder: "1, 10, 999",
  },

  // Spacing
  {
    key: "padding",
    label: "Padding",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem, 10px 20px",
  },
  {
    key: "paddingTop",
    label: "Padding Top",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },
  {
    key: "paddingRight",
    label: "Padding Right",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },
  {
    key: "paddingBottom",
    label: "Padding Bottom",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },
  {
    key: "paddingLeft",
    label: "Padding Left",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },
  {
    key: "margin",
    label: "Margin",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem, 10px 20px",
  },
  {
    key: "marginTop",
    label: "Margin Top",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },
  {
    key: "marginBottom",
    label: "Margin Bottom",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },
  {
    key: "marginLeft",
    label: "Margin Left",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },
  {
    key: "marginRight",
    label: "Margin Right",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },

  // Dimensions
  {
    key: "width",
    label: "Width",
    type: "input",
    category: "Dimensions",
    placeholder: "100px, 50%, auto, 100vw",
  },
  {
    key: "height",
    label: "Height",
    type: "input",
    category: "Dimensions",
    placeholder: "100px, 50%, auto, 100vh",
  },
  {
    key: "minWidth",
    label: "Min Width",
    type: "input",
    category: "Dimensions",
    placeholder: "100px, 50%",
  },
  {
    key: "minHeight",
    label: "Min Height",
    type: "input",
    category: "Dimensions",
    placeholder: "100px, 50%",
  },
  {
    key: "maxWidth",
    label: "Max Width",
    type: "input",
    category: "Dimensions",
    placeholder: "500px, 100%",
  },
  {
    key: "maxHeight",
    label: "Max Height",
    type: "input",
    category: "Dimensions",
    placeholder: "300px, 100%",
  },

  // Colors & Background
  {
    key: "backgroundColor",
    label: "Background Color",
    type: "input",
    category: "Colors & Background",
    placeholder: "#ffffff, rgb(255,255,255)",
  },
  {
    key: "backgroundImage",
    label: "Background Image",
    type: "input",
    category: "Colors & Background",
    placeholder: "url(image.jpg), linear-gradient(...)",
  },
  {
    key: "backgroundSize",
    label: "Background Size",
    type: "select",
    category: "Colors & Background",
    options: [
      { value: "default", label: "Default" },
      { value: "auto", label: "Auto" },
      { value: "cover", label: "Cover" },
      { value: "contain", label: "Contain" },
      { value: "100%", label: "100%" },
      { value: "100% 100%", label: "100% 100%" },
    ],
  },
  {
    key: "backgroundPosition",
    label: "Background Position",
    type: "select",
    category: "Colors & Background",
    options: [
      { value: "default", label: "Default" },
      { value: "center", label: "Center" },
      { value: "top", label: "Top" },
      { value: "bottom", label: "Bottom" },
      { value: "left", label: "Left" },
      { value: "right", label: "Right" },
      { value: "center center", label: "Center Center" },
      { value: "top left", label: "Top Left" },
      { value: "top right", label: "Top Right" },
      { value: "bottom left", label: "Bottom Left" },
      { value: "bottom right", label: "Bottom Right" },
    ],
  },
  {
    key: "backgroundRepeat",
    label: "Background Repeat",
    type: "select",
    category: "Colors & Background",
    options: [
      { value: "default", label: "Default" },
      { value: "repeat", label: "Repeat" },
      { value: "no-repeat", label: "No Repeat" },
      { value: "repeat-x", label: "Repeat X" },
      { value: "repeat-y", label: "Repeat Y" },
    ],
  },
  {
    key: "color",
    label: "Text Color",
    type: "input",
    category: "Colors & Background",
    placeholder: "#000000, rgb(0,0,0)",
  },
  {
    key: "opacity",
    label: "Opacity",
    type: "input",
    category: "Colors & Background",
    placeholder: "0.5, 1",
  },

  // Typography
  {
    key: "fontFamily",
    label: "Font Family",
    type: "input",
    category: "Typography",
    placeholder: "Arial, sans-serif",
  },
  {
    key: "fontSize",
    label: "Font Size",
    type: "input",
    category: "Typography",
    placeholder: "16px, 1rem, 1.2em",
  },
  {
    key: "fontWeight",
    label: "Font Weight",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "100", label: "Thin (100)" },
      { value: "300", label: "Light (300)" },
      { value: "400", label: "Normal (400)" },
      { value: "500", label: "Medium (500)" },
      { value: "600", label: "Semi Bold (600)" },
      { value: "700", label: "Bold (700)" },
      { value: "900", label: "Black (900)" },
    ],
  },
  {
    key: "fontStyle",
    label: "Font Style",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "normal", label: "Normal" },
      { value: "italic", label: "Italic" },
      { value: "oblique", label: "Oblique" },
    ],
  },
  {
    key: "textAlign",
    label: "Text Align",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "left", label: "Left" },
      { value: "center", label: "Center" },
      { value: "right", label: "Right" },
      { value: "justify", label: "Justify" },
    ],
  },
  {
    key: "textDecoration",
    label: "Text Decoration",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "none", label: "None" },
      { value: "underline", label: "Underline" },
      { value: "overline", label: "Overline" },
      { value: "line-through", label: "Line Through" },
    ],
  },
  {
    key: "textTransform",
    label: "Text Transform",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "none", label: "None" },
      { value: "uppercase", label: "Uppercase" },
      { value: "lowercase", label: "Lowercase" },
      { value: "capitalize", label: "Capitalize" },
    ],
  },
  {
    key: "lineHeight",
    label: "Line Height",
    type: "input",
    category: "Typography",
    placeholder: "1.5, 24px, 1.2em",
  },
  {
    key: "letterSpacing",
    label: "Letter Spacing",
    type: "input",
    category: "Typography",
    placeholder: "1px, 0.1em",
  },
  {
    key: "wordSpacing",
    label: "Word Spacing",
    type: "input",
    category: "Typography",
    placeholder: "2px, 0.2em",
  },
  {
    key: "textShadow",
    label: "Text Shadow",
    type: "input",
    category: "Typography",
    placeholder: "1px 1px 2px rgba(0,0,0,0.5)",
  },
  {
    key: "whiteSpace",
    label: "White Space",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "normal", label: "Normal" },
      { value: "nowrap", label: "No Wrap" },
      { value: "pre", label: "Pre" },
      { value: "pre-wrap", label: "Pre Wrap" },
      { value: "pre-line", label: "Pre Line" },
    ],
  },
  {
    key: "wordBreak",
    label: "Word Break",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "normal", label: "Normal" },
      { value: "break-all", label: "Break All" },
      { value: "keep-all", label: "Keep All" },
      { value: "break-word", label: "Break Word" },
    ],
  },

  // Borders
  {
    key: "border",
    label: "Border",
    type: "input",
    category: "Borders",
    placeholder: "1px solid #000",
  },
  {
    key: "borderTop",
    label: "Border Top",
    type: "input",
    category: "Borders",
    placeholder: "1px solid #000",
  },
  {
    key: "borderRight",
    label: "Border Right",
    type: "input",
    category: "Borders",
    placeholder: "1px solid #000",
  },
  {
    key: "borderBottom",
    label: "Border Bottom",
    type: "input",
    category: "Borders",
    placeholder: "1px solid #000",
  },
  {
    key: "borderLeft",
    label: "Border Left",
    type: "input",
    category: "Borders",
    placeholder: "1px solid #000",
  },
  {
    key: "borderRadius",
    label: "Border Radius",
    type: "input",
    category: "Borders",
    placeholder: "5px, 50%, 10px 20px",
  },
  {
    key: "borderColor",
    label: "Border Color",
    type: "input",
    category: "Borders",
    placeholder: "#000000, rgb(0,0,0)",
  },
  {
    key: "borderWidth",
    label: "Border Width",
    type: "input",
    category: "Borders",
    placeholder: "1px, 2px, thin",
  },
  {
    key: "borderStyle",
    label: "Border Style",
    type: "select",
    category: "Borders",
    options: [
      { value: "default", label: "Default" },
      { value: "none", label: "None" },
      { value: "solid", label: "Solid" },
      { value: "dashed", label: "Dashed" },
      { value: "dotted", label: "Dotted" },
      { value: "double", label: "Double" },
      { value: "groove", label: "Groove" },
      { value: "ridge", label: "Ridge" },
      { value: "inset", label: "Inset" },
      { value: "outset", label: "Outset" },
    ],
  },

  // Effects
  {
    key: "boxShadow",
    label: "Box Shadow",
    type: "input",
    category: "Effects",
    placeholder: "0 2px 4px rgba(0,0,0,0.1)",
  },
  {
    key: "transform",
    label: "Transform",
    type: "input",
    category: "Effects",
    placeholder: "rotate(45deg), scale(1.1)",
  },
  {
    key: "transition",
    label: "Transition",
    type: "input",
    category: "Effects",
    placeholder: "all 0.3s ease",
  },
  {
    key: "animation",
    label: "Animation",
    type: "input",
    category: "Effects",
    placeholder: "fadeIn 1s ease-in-out",
  },
  {
    key: "filter",
    label: "Filter",
    type: "input",
    category: "Effects",
    placeholder: "blur(5px), brightness(1.2), contrast(1.1)",
  },
  {
    key: "backdropFilter",
    label: "Backdrop Filter",
    type: "input",
    category: "Effects",
    placeholder: "blur(10px), brightness(0.8)",
  },
  {
    key: "clipPath",
    label: "Clip Path",
    type: "input",
    category: "Effects",
    placeholder: "circle(50%), polygon(0 0, 100% 0, 100% 100%)",
  },

  // Flexbox & Grid
  {
    key: "flexWrap",
    label: "Flex Wrap",
    type: "select",
    category: "Flexbox & Grid",
    options: [
      { value: "default", label: "Default" },
      { value: "nowrap", label: "No Wrap" },
      { value: "wrap", label: "Wrap" },
      { value: "wrap-reverse", label: "Wrap Reverse" },
    ],
  },
  {
    key: "flexGrow",
    label: "Flex Grow",
    type: "input",
    category: "Flexbox & Grid",
    placeholder: "0, 1, 2",
  },
  {
    key: "flexShrink",
    label: "Flex Shrink",
    type: "input",
    category: "Flexbox & Grid",
    placeholder: "0, 1",
  },
  {
    key: "flexBasis",
    label: "Flex Basis",
    type: "input",
    category: "Flexbox & Grid",
    placeholder: "auto, 200px, 50%",
  },
  {
    key: "alignSelf",
    label: "Align Self",
    type: "select",
    category: "Flexbox & Grid",
    options: [
      { value: "default", label: "Default" },
      { value: "auto", label: "Auto" },
      { value: "flex-start", label: "Flex Start" },
      { value: "flex-end", label: "Flex End" },
      { value: "center", label: "Center" },
      { value: "baseline", label: "Baseline" },
      { value: "stretch", label: "Stretch" },
    ],
  },
  {
    key: "justifySelf",
    label: "Justify Self",
    type: "select",
    category: "Flexbox & Grid",
    options: [
      { value: "default", label: "Default" },
      { value: "auto", label: "Auto" },
      { value: "start", label: "Start" },
      { value: "end", label: "End" },
      { value: "center", label: "Center" },
      { value: "stretch", label: "Stretch" },
    ],
  },
  {
    key: "order",
    label: "Order",
    type: "input",
    category: "Flexbox & Grid",
    placeholder: "0, 1, -1",
  },
  {
    key: "gridTemplateColumns",
    label: "Grid Template Columns",
    type: "input",
    category: "Flexbox & Grid",
    placeholder: "1fr 1fr, repeat(3, 1fr), 200px auto",
  },
  {
    key: "gridTemplateRows",
    label: "Grid Template Rows",
    type: "input",
    category: "Flexbox & Grid",
    placeholder: "auto, 100px 200px, repeat(2, 1fr)",
  },
  {
    key: "gridColumn",
    label: "Grid Column",
    type: "input",
    category: "Flexbox & Grid",
    placeholder: "1 / 3, span 2",
  },
  {
    key: "gridRow",
    label: "Grid Row",
    type: "input",
    category: "Flexbox & Grid",
    placeholder: "1 / 3, span 2",
  },
  {
    key: "gridArea",
    label: "Grid Area",
    type: "input",
    category: "Flexbox & Grid",
    placeholder: "header, 1 / 1 / 3 / 3",
  },

  // Positioning
  {
    key: "top",
    label: "Top",
    type: "input",
    category: "Positioning",
    placeholder: "10px, 50%, auto",
  },
  {
    key: "right",
    label: "Right",
    type: "input",
    category: "Positioning",
    placeholder: "10px, 50%, auto",
  },
  {
    key: "bottom",
    label: "Bottom",
    type: "input",
    category: "Positioning",
    placeholder: "10px, 50%, auto",
  },
  {
    key: "left",
    label: "Left",
    type: "input",
    category: "Positioning",
    placeholder: "10px, 50%, auto",
  },

  // Media & Object
  {
    key: "objectFit",
    label: "Object Fit",
    type: "select",
    category: "Media & Object",
    options: [
      { value: "default", label: "Default" },
      { value: "fill", label: "Fill" },
      { value: "contain", label: "Contain" },
      { value: "cover", label: "Cover" },
      { value: "scale-down", label: "Scale Down" },
      { value: "none", label: "None" },
    ],
  },
  {
    key: "objectPosition",
    label: "Object Position",
    type: "select",
    category: "Media & Object",
    options: [
      { value: "default", label: "Default" },
      { value: "center", label: "Center" },
      { value: "top", label: "Top" },
      { value: "bottom", label: "Bottom" },
      { value: "left", label: "Left" },
      { value: "right", label: "Right" },
      { value: "top left", label: "Top Left" },
      { value: "top right", label: "Top Right" },
      { value: "bottom left", label: "Bottom Left" },
      { value: "bottom right", label: "Bottom Right" },
    ],
  },
  {
    key: "resize",
    label: "Resize",
    type: "select",
    category: "Media & Object",
    options: [
      { value: "default", label: "Default" },
      { value: "none", label: "None" },
      { value: "both", label: "Both" },
      { value: "horizontal", label: "Horizontal" },
      { value: "vertical", label: "Vertical" },
    ],
  },

  // Interactive
  {
    key: "cursor",
    label: "Cursor",
    type: "select",
    category: "Interactive",
    options: [
      { value: "default", label: "Default" },
      { value: "auto", label: "Auto" },
      { value: "pointer", label: "Pointer" },
      { value: "text", label: "Text" },
      { value: "move", label: "Move" },
      { value: "grab", label: "Grab" },
      { value: "grabbing", label: "Grabbing" },
      { value: "not-allowed", label: "Not Allowed" },
      { value: "wait", label: "Wait" },
      { value: "help", label: "Help" },
      { value: "crosshair", label: "Crosshair" },
    ],
  },
  {
    key: "pointerEvents",
    label: "Pointer Events",
    type: "select",
    category: "Interactive",
    options: [
      { value: "default", label: "Default" },
      { value: "auto", label: "Auto" },
      { value: "none", label: "None" },
      { value: "all", label: "All" },
    ],
  },
  {
    key: "userSelect",
    label: "User Select",
    type: "select",
    category: "Interactive",
    options: [
      { value: "default", label: "Default" },
      { value: "auto", label: "Auto" },
      { value: "none", label: "None" },
      { value: "text", label: "Text" },
      { value: "all", label: "All" },
    ],
  },
  {
    key: "visibility",
    label: "Visibility",
    type: "select",
    category: "Interactive",
    options: [
      { value: "default", label: "Default" },
      { value: "visible", label: "Visible" },
      { value: "hidden", label: "Hidden" },
      { value: "collapse", label: "Collapse" },
    ],
  },
  {
    key: "overflow",
    label: "Overflow",
    type: "select",
    category: "Interactive",
    options: [
      { value: "default", label: "Default" },
      { value: "visible", label: "Visible" },
      { value: "hidden", label: "Hidden" },
      { value: "scroll", label: "Scroll" },
      { value: "auto", label: "Auto" },
    ],
  },
  {
    key: "overflowX",
    label: "Overflow X",
    type: "select",
    category: "Interactive",
    options: [
      { value: "default", label: "Default" },
      { value: "visible", label: "Visible" },
      { value: "hidden", label: "Hidden" },
      { value: "scroll", label: "Scroll" },
      { value: "auto", label: "Auto" },
    ],
  },
  {
    key: "overflowY",
    label: "Overflow Y",
    type: "select",
    category: "Interactive",
    options: [
      { value: "default", label: "Default" },
      { value: "visible", label: "Visible" },
      { value: "hidden", label: "Hidden" },
      { value: "scroll", label: "Scroll" },
      { value: "auto", label: "Auto" },
    ],
  },

  // Advanced Properties
  {
    key: "mixBlendMode",
    label: "Mix Blend Mode",
    type: "select",
    category: "Advanced",
    options: [
      { value: "default", label: "Default" },
      { value: "normal", label: "Normal" },
      { value: "multiply", label: "Multiply" },
      { value: "screen", label: "Screen" },
      { value: "overlay", label: "Overlay" },
      { value: "darken", label: "Darken" },
      { value: "lighten", label: "Lighten" },
      { value: "color-dodge", label: "Color Dodge" },
      { value: "color-burn", label: "Color Burn" },
      { value: "hard-light", label: "Hard Light" },
      { value: "soft-light", label: "Soft Light" },
      { value: "difference", label: "Difference" },
      { value: "exclusion", label: "Exclusion" },
    ],
  },
  {
    key: "isolation",
    label: "Isolation",
    type: "select",
    category: "Advanced",
    options: [
      { value: "default", label: "Default" },
      { value: "auto", label: "Auto" },
      { value: "isolate", label: "Isolate" },
    ],
  },
  {
    key: "willChange",
    label: "Will Change",
    type: "input",
    category: "Advanced",
    placeholder: "transform, opacity, auto",
  },
  {
    key: "contain",
    label: "Contain",
    type: "select",
    category: "Advanced",
    options: [
      { value: "default", label: "Default" },
      { value: "none", label: "None" },
      { value: "strict", label: "Strict" },
      { value: "content", label: "Content" },
      { value: "size", label: "Size" },
      { value: "layout", label: "Layout" },
      { value: "style", label: "Style" },
      { value: "paint", label: "Paint" },
    ],
  },
  {
    key: "aspectRatio",
    label: "Aspect Ratio",
    type: "input",
    category: "Advanced",
    placeholder: "16/9, 1/1, 4/3",
  },
  {
    key: "scrollBehavior",
    label: "Scroll Behavior",
    type: "select",
    category: "Advanced",
    options: [
      { value: "default", label: "Default" },
      { value: "auto", label: "Auto" },
      { value: "smooth", label: "Smooth" },
    ],
  },
];

// Specialized property configurations for different entity types

// Image-specific CSS properties
export const IMAGE_CSS_PROPERTIES: CssPropertyConfig[] = [
  // Layout & Display
  {
    key: "display",
    label: "Display",
    type: "select",
    category: "Layout & Display",
    options: [
      { value: "default", label: "Default" },
      { value: "block", label: "Block" },
      { value: "inline", label: "Inline" },
      { value: "inline-block", label: "Inline Block" },
      { value: "none", label: "None" },
    ],
  },
  {
    key: "position",
    label: "Position",
    type: "select",
    category: "Layout & Display",
    options: [
      { value: "default", label: "Default" },
      { value: "relative", label: "Relative" },
      { value: "absolute", label: "Absolute" },
      { value: "fixed", label: "Fixed" },
    ],
  },

  // Sizing
  {
    key: "width",
    label: "Width",
    type: "input",
    category: "Sizing",
    placeholder: "100px, 50%, auto",
  },
  {
    key: "height",
    label: "Height",
    type: "input",
    category: "Sizing",
    placeholder: "100px, 50%, auto",
  },
  {
    key: "maxWidth",
    label: "Max Width",
    type: "input",
    category: "Sizing",
    placeholder: "500px, 100%",
  },
  {
    key: "maxHeight",
    label: "Max Height",
    type: "input",
    category: "Sizing",
    placeholder: "300px, 100%",
  },
  {
    key: "objectFit",
    label: "Object Fit",
    type: "select",
    category: "Sizing",
    options: [
      { value: "default", label: "Default" },
      { value: "fill", label: "Fill" },
      { value: "contain", label: "Contain" },
      { value: "cover", label: "Cover" },
      { value: "scale-down", label: "Scale Down" },
      { value: "none", label: "None" },
    ],
  },

  // Spacing
  {
    key: "margin",
    label: "Margin",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },
  {
    key: "padding",
    label: "Padding",
    type: "input",
    category: "Spacing",
    placeholder: "10px, 1rem",
  },

  // Borders & Effects
  {
    key: "borderRadius",
    label: "Border Radius",
    type: "input",
    category: "Borders & Effects",
    placeholder: "5px, 50%",
  },
  {
    key: "border",
    label: "Border",
    type: "input",
    category: "Borders & Effects",
    placeholder: "1px solid #000",
  },
  {
    key: "boxShadow",
    label: "Box Shadow",
    type: "input",
    category: "Borders & Effects",
    placeholder: "0 2px 4px rgba(0,0,0,0.1)",
  },
  {
    key: "filter",
    label: "Filter",
    type: "input",
    category: "Borders & Effects",
    placeholder: "blur(5px), brightness(1.2)",
  },
  {
    key: "opacity",
    label: "Opacity",
    type: "input",
    category: "Borders & Effects",
    placeholder: "0.5, 1",
  },

  // Transform & Animation
  {
    key: "transform",
    label: "Transform",
    type: "input",
    category: "Transform & Animation",
    placeholder: "rotate(45deg), scale(1.1)",
  },
  {
    key: "transition",
    label: "Transition",
    type: "input",
    category: "Transform & Animation",
    placeholder: "all 0.3s ease",
  },

  // Interactive
  {
    key: "cursor",
    label: "Cursor",
    type: "select",
    category: "Interactive",
    options: [
      { value: "default", label: "Default" },
      { value: "pointer", label: "Pointer" },
      { value: "zoom-in", label: "Zoom In" },
      { value: "grab", label: "Grab" },
    ],
  },
];

// Button-specific CSS properties
export const BUTTON_CSS_PROPERTIES: CssPropertyConfig[] = [
  // Layout & Display
  {
    key: "display",
    label: "Display",
    type: "select",
    category: "Layout & Display",
    options: [
      { value: "default", label: "Default" },
      { value: "inline-block", label: "Inline Block" },
      { value: "block", label: "Block" },
      { value: "flex", label: "Flex" },
      { value: "inline-flex", label: "Inline Flex" },
    ],
  },

  // Sizing
  {
    key: "width",
    label: "Width",
    type: "input",
    category: "Sizing",
    placeholder: "auto, 200px, 100%",
  },
  {
    key: "height",
    label: "Height",
    type: "input",
    category: "Sizing",
    placeholder: "auto, 40px",
  },
  {
    key: "minWidth",
    label: "Min Width",
    type: "input",
    category: "Sizing",
    placeholder: "100px",
  },

  // Spacing
  {
    key: "padding",
    label: "Padding",
    type: "input",
    category: "Spacing",
    placeholder: "10px 20px",
  },
  {
    key: "margin",
    label: "Margin",
    type: "input",
    category: "Spacing",
    placeholder: "10px",
  },

  // Typography
  {
    key: "fontSize",
    label: "Font Size",
    type: "input",
    category: "Typography",
    placeholder: "16px, 1rem",
  },
  {
    key: "fontWeight",
    label: "Font Weight",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "400", label: "Normal" },
      { value: "500", label: "Medium" },
      { value: "600", label: "Semi Bold" },
      { value: "700", label: "Bold" },
    ],
  },
  {
    key: "textAlign",
    label: "Text Align",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "left", label: "Left" },
      { value: "center", label: "Center" },
      { value: "right", label: "Right" },
    ],
  },
  {
    key: "textTransform",
    label: "Text Transform",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "none", label: "None" },
      { value: "uppercase", label: "Uppercase" },
      { value: "lowercase", label: "Lowercase" },
      { value: "capitalize", label: "Capitalize" },
    ],
  },

  // Colors & Background
  {
    key: "backgroundColor",
    label: "Background Color",
    type: "input",
    category: "Colors & Background",
    placeholder: "#007bff, rgb(0,123,255)",
  },
  {
    key: "color",
    label: "Text Color",
    type: "input",
    category: "Colors & Background",
    placeholder: "#ffffff, white",
  },
  {
    key: "backgroundImage",
    label: "Background Image",
    type: "input",
    category: "Colors & Background",
    placeholder: "linear-gradient(...)",
  },

  // Borders & Effects
  {
    key: "border",
    label: "Border",
    type: "input",
    category: "Borders & Effects",
    placeholder: "1px solid #007bff",
  },
  {
    key: "borderRadius",
    label: "Border Radius",
    type: "input",
    category: "Borders & Effects",
    placeholder: "4px, 50%",
  },
  {
    key: "boxShadow",
    label: "Box Shadow",
    type: "input",
    category: "Borders & Effects",
    placeholder: "0 2px 4px rgba(0,0,0,0.1)",
  },

  // Interactive States
  {
    key: "cursor",
    label: "Cursor",
    type: "select",
    category: "Interactive",
    options: [
      { value: "default", label: "Default" },
      { value: "pointer", label: "Pointer" },
      { value: "not-allowed", label: "Not Allowed" },
    ],
  },
  {
    key: "transition",
    label: "Transition",
    type: "input",
    category: "Interactive",
    placeholder: "all 0.2s ease",
  },
  {
    key: "transform",
    label: "Transform",
    type: "input",
    category: "Interactive",
    placeholder: "scale(1.05)",
  },
];

// Text/Form-specific CSS properties
export const TEXT_CSS_PROPERTIES: CssPropertyConfig[] = [
  // Typography
  {
    key: "fontFamily",
    label: "Font Family",
    type: "input",
    category: "Typography",
    placeholder: "Arial, sans-serif",
  },
  {
    key: "fontSize",
    label: "Font Size",
    type: "input",
    category: "Typography",
    placeholder: "16px, 1rem",
  },
  {
    key: "fontWeight",
    label: "Font Weight",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "300", label: "Light" },
      { value: "400", label: "Normal" },
      { value: "500", label: "Medium" },
      { value: "600", label: "Semi Bold" },
      { value: "700", label: "Bold" },
    ],
  },
  {
    key: "fontStyle",
    label: "Font Style",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "normal", label: "Normal" },
      { value: "italic", label: "Italic" },
    ],
  },
  {
    key: "textAlign",
    label: "Text Align",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "left", label: "Left" },
      { value: "center", label: "Center" },
      { value: "right", label: "Right" },
      { value: "justify", label: "Justify" },
    ],
  },
  {
    key: "lineHeight",
    label: "Line Height",
    type: "input",
    category: "Typography",
    placeholder: "1.5, 24px",
  },
  {
    key: "letterSpacing",
    label: "Letter Spacing",
    type: "input",
    category: "Typography",
    placeholder: "1px, 0.1em",
  },
  {
    key: "textDecoration",
    label: "Text Decoration",
    type: "select",
    category: "Typography",
    options: [
      { value: "default", label: "Default" },
      { value: "none", label: "None" },
      { value: "underline", label: "Underline" },
      { value: "line-through", label: "Line Through" },
    ],
  },

  // Colors
  {
    key: "color",
    label: "Text Color",
    type: "input",
    category: "Colors",
    placeholder: "#000000, rgb(0,0,0)",
  },
  {
    key: "backgroundColor",
    label: "Background Color",
    type: "input",
    category: "Colors",
    placeholder: "transparent, #ffffff",
  },

  // Spacing
  {
    key: "padding",
    label: "Padding",
    type: "input",
    category: "Spacing",
    placeholder: "10px",
  },
  {
    key: "margin",
    label: "Margin",
    type: "input",
    category: "Spacing",
    placeholder: "10px 0",
  },

  // Effects
  {
    key: "textShadow",
    label: "Text Shadow",
    type: "input",
    category: "Effects",
    placeholder: "1px 1px 2px rgba(0,0,0,0.5)",
  },
  {
    key: "opacity",
    label: "Opacity",
    type: "input",
    category: "Effects",
    placeholder: "1, 0.8",
  },
];
